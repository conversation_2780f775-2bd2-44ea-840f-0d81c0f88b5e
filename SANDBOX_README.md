# SynTour 沙箱环境

## 🎯 概述

SynTour沙箱环境是一个完全隔离的开发测试环境，使用Docker容器化技术，让您可以安全地测试和开发SynTour项目的各项功能。

## 🏗️ 架构组件

### 核心服务
- **前端服务** (Next.js): 端口 3007
- **后端服务** (FastAPI): 端口 8000  
- **Nginx代理**: 端口 80/443
- **PostgreSQL数据库**: 端口 5432
- **Redis缓存**: 端口 6379

### 技术栈
- **前端**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **后端**: FastAPI, Python 3.11, Uvicorn
- **数据库**: PostgreSQL 15
- **缓存**: Redis 7
- **代理**: Nginx Alpine
- **容器化**: Docker & Docker Compose

## 🚀 快速开始

### 前置要求
- Windows 10/11 或 macOS 或 Linux
- Docker Desktop 4.0+
- 至少 4GB 可用内存
- 至少 10GB 可用磁盘空间

### 一键启动
```bash
# Windows用户
.\sandbox-start.bat

# Linux/macOS用户
./sandbox-start.sh
```

### 手动启动
```bash
# 1. 构建并启动所有服务
docker-compose -f docker-compose.sandbox.yml --env-file .env.sandbox up --build -d

# 2. 查看服务状态
docker-compose -f docker-compose.sandbox.yml ps

# 3. 查看日志
docker-compose -f docker-compose.sandbox.yml logs -f
```

## 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 🌐 前端应用 | http://localhost | 主要用户界面 |
| 🔧 后端API | http://localhost/api | RESTful API接口 |
| 📊 API文档 | http://localhost/api/docs | Swagger文档 |
| 🗄️ 数据库 | localhost:5432 | PostgreSQL |
| 🗃️ Redis | localhost:6379 | 缓存服务 |

## 🛠️ 管理工具

### 沙箱管理脚本
```bash
# Windows
.\sandbox-manage.bat

# 提供以下功能：
# - 启动/停止/重启环境
# - 查看服务状态和日志
# - 进入容器终端
# - 健康检查
# - 数据备份和清理
```

### 常用命令
```bash
# 停止环境
docker-compose -f docker-compose.sandbox.yml down

# 重启特定服务
docker-compose -f docker-compose.sandbox.yml restart frontend

# 查看特定服务日志
docker-compose -f docker-compose.sandbox.yml logs -f backend

# 进入容器
docker-compose -f docker-compose.sandbox.yml exec backend bash
docker-compose -f docker-compose.sandbox.yml exec frontend sh

# 执行数据库命令
docker-compose -f docker-compose.sandbox.yml exec postgres psql -U syntour -d syntour_dev
```

## 🔧 配置说明

### 环境变量
主要配置文件：`.env.sandbox`

```env
# 基础配置
NODE_ENV=development
ENVIRONMENT=sandbox

# 前端配置
NEXT_PUBLIC_API_URL=http://localhost/api
NEXT_PUBLIC_WS_URL=ws://localhost/ws

# 数据库配置
DATABASE_URL=*******************************************************/syntour_dev

# Google Cloud配置（可选）
GOOGLE_CLOUD_PROJECT=your-sandbox-project-id
GOOGLE_CLOUD_LOCATION=us-central1
```

### Google Cloud集成
如需使用AI功能，请参考：`sandbox-google-cloud-setup.md`

## 📁 目录结构

```
syntour/
├── docker-compose.sandbox.yml    # Docker Compose配置
├── Dockerfile.frontend           # 前端容器配置
├── Dockerfile.backend            # 后端容器配置
├── .env.sandbox                  # 沙箱环境变量
├── sandbox-start.bat             # 启动脚本(Windows)
├── sandbox-manage.bat            # 管理脚本(Windows)
├── nginx/
│   └── sandbox.conf              # Nginx配置
├── backend/
│   ├── config/                   # 后端配置目录
│   └── requirements-core.txt     # Python依赖
└── uploads/                      # 文件上传目录
```

## 🔍 开发调试

### 热重载
- **前端**: 支持Next.js热重载，代码修改后自动刷新
- **后端**: 支持FastAPI热重载，Python代码修改后自动重启

### 调试工具
```bash
# 查看容器资源使用
docker stats

# 查看网络连接
docker network ls
docker network inspect syntour_syntour-network

# 查看卷挂载
docker volume ls
```

### 日志调试
```bash
# 查看所有服务日志
docker-compose -f docker-compose.sandbox.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.sandbox.yml logs frontend
docker-compose -f docker-compose.sandbox.yml logs backend
docker-compose -f docker-compose.sandbox.yml logs postgres

# 实时跟踪日志
docker-compose -f docker-compose.sandbox.yml logs -f --tail=100
```

## 🧪 测试功能

### 健康检查
```bash
# 自动健康检查
curl http://localhost/health
curl http://localhost/api/health

# 数据库连接测试
docker-compose -f docker-compose.sandbox.yml exec postgres pg_isready -U syntour

# Redis连接测试
docker-compose -f docker-compose.sandbox.yml exec redis redis-cli ping
```

### 功能测试
1. **前端界面**: 访问 http://localhost
2. **API接口**: 访问 http://localhost/api/docs
3. **文件上传**: 测试图片和文档上传功能
4. **AI功能**: 测试图像识别和文本处理（需配置Google Cloud）

## 📊 性能监控

### 资源监控
```bash
# 查看容器资源使用
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"

# 查看磁盘使用
docker system df
```

### 性能优化建议
- 为Docker Desktop分配至少4GB内存
- 启用Docker Desktop的WSL2后端（Windows）
- 定期清理未使用的镜像和容器

## 🔒 安全注意事项

1. **沙箱隔离**: 环境完全隔离，不影响主机系统
2. **数据安全**: 测试数据不会影响生产环境
3. **网络安全**: 仅开放必要端口，内部服务通过Docker网络通信
4. **密钥管理**: 使用测试密钥，不要使用生产密钥

## 🧹 清理和维护

### 日常清理
```bash
# 停止并删除容器
docker-compose -f docker-compose.sandbox.yml down

# 清理未使用的镜像
docker image prune -f

# 清理未使用的卷
docker volume prune -f

# 完全清理（包括数据）
docker-compose -f docker-compose.sandbox.yml down -v
```

### 数据备份
```bash
# 使用管理脚本备份
.\sandbox-manage.bat  # 选择选项9

# 手动备份数据库
docker-compose -f docker-compose.sandbox.yml exec postgres pg_dump -U syntour syntour_dev > backup.sql
```

## ❓ 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -ano | findstr :80
   netstat -ano | findstr :3007
   ```

2. **内存不足**
   ```bash
   # 增加Docker内存限制
   # Docker Desktop -> Settings -> Resources -> Memory
   ```

3. **构建失败**
   ```bash
   # 清理构建缓存
   docker-compose -f docker-compose.sandbox.yml build --no-cache
   ```

4. **网络问题**
   ```bash
   # 重建网络
   docker network prune
   docker-compose -f docker-compose.sandbox.yml up --force-recreate
   ```

### 获取帮助
- 查看容器日志获取详细错误信息
- 检查Docker Desktop状态
- 确保防火墙不阻止Docker端口
- 参考项目文档和GitHub Issues

## 📝 更新日志

- **v1.0.0**: 初始沙箱环境发布
- 支持完整的前后端开发环境
- 集成数据库和缓存服务
- 提供管理工具和脚本

---

🎉 **享受在SynTour沙箱环境中的开发体验！**