"use client";
import Link from "next/link";
import { useState } from "react";
export default function Forgot(){
  const [email,setEmail] = useState("");
  async function submit(e:React.FormEvent){ e.preventDefault();
    const r = await fetch("/api/auth/forgot",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email})});
    if(r.ok) alert("Password reset email sent (mock)."); else alert("Failed.");
  }
  return (
    <div className="min-h-screen container-page flex items-center">
      <form onSubmit={submit} className="card p-8 w-full max-w-md mx-auto">
        <h1 className="text-2xl font-semibold mb-4">Reset your password</h1>
        <input className="input mb-4" type="email" placeholder="Email" value={email} onChange={e=>setEmail(e.target.value)} required/>
        <button className="btn btn-primary w-full">Send reset link</button>
        <p className="text-sm mt-3"><Link className="link" href="/auth/login">Back to sign in</Link></p>
      </form>
    </div>
  );
}
