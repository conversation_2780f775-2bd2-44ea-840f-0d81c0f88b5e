from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON>earer
import os
from contextlib import asynccontextmanager
import logging

# 导入路由
from api.auth import router as auth_router
from database import init_database, check_database_connection

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    logger.info("启动应用...")
    
    # 检查数据库连接
    if not check_database_connection():
        logger.error("数据库连接失败")
        raise HTTPException(status_code=500, detail="数据库连接失败")
    
    # 初始化数据库
    try:
        init_database()
        logger.info("数据库初始化成功")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise HTTPException(status_code=500, detail="数据库初始化失败")
    
    logger.info("应用启动完成")
    yield
    
    # 关闭时执行
    logger.info("应用关闭")

# 创建FastAPI应用
app = FastAPI(
    title="SynTour API",
    description="SynTour 旅游平台后端API",
    version="1.0.0",
    lifespan=lifespan
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # Next.js开发服务器
        "http://127.0.0.1:3000",
        "https://syntour.vercel.app",  # 生产环境域名
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 安全配置
security = HTTPBearer()

# 注册路由
app.include_router(auth_router, prefix="/api/auth", tags=["认证"])

# 根路径
@app.get("/")
async def root():
    return {
        "message": "Welcome to SynTour API",
        "version": "1.0.0",
        "status": "running"
    }

# 健康检查
@app.get("/health")
async def health_check():
    db_status = check_database_connection()
    return {
        "status": "healthy" if db_status else "unhealthy",
        "database": "connected" if db_status else "disconnected",
        "version": "1.0.0"
    }

# 错误处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    return {"detail": exc.detail}

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"未处理的异常: {str(exc)}")
    return {"detail": "内部服务器错误"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )