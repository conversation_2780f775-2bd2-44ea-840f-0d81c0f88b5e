# SynTour 生产环境部署指南

## 服务器信息
- **服务器IP**: ************
- **操作系统**: Ubuntu 22.04.5 LTS
- **架构**: x86_64
- **内存**: 1.6GB
- **存储**: 40GB

## 部署步骤

### 1. 连接到服务器
```bash
ssh root@************
# 密码: Lw20060607.@@
```

### 2. 系统准备
```bash
# 更新系统
apt update && apt upgrade -y

# 安装必要工具
apt install -y curl wget git vim htop unzip

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

### 3. 克隆项目代码
```bash
# 创建应用目录
mkdir -p /opt/syntour
cd /opt/syntour

# 克隆代码
git clone https://github.com/SunflowersLwtech/SynTour.git .

# 设置权限
chown -R root:root /opt/syntour
```

### 4. 配置环境变量
```bash
# 复制环境配置文件
cp .env.production .env

# 编辑配置文件（根据实际情况修改）
vim .env
```

**重要配置项**:
- `POSTGRES_PASSWORD`: 数据库密码
- `REDIS_PASSWORD`: Redis密码
- `JWT_SECRET`: JWT密钥（必须更改）
- `SECRET_KEY`: 应用密钥（必须更改）

### 5. 创建必要目录
```bash
mkdir -p logs nginx/ssl uploads
chmod -R 755 logs uploads
```

### 6. 启动服务
```bash
# 构建并启动所有服务
docker-compose -f docker-compose.production.yml up --build -d

# 查看服务状态
docker-compose -f docker-compose.production.yml ps

# 查看日志
docker-compose -f docker-compose.production.yml logs -f
```

### 7. 验证部署
```bash
# 检查服务健康状态
curl http://localhost/health

# 检查API
curl http://localhost/api/v1/health

# 检查前端
curl http://localhost
```

## 服务管理

### 启动服务
```bash
cd /opt/syntour
docker-compose -f docker-compose.production.yml up -d
```

### 停止服务
```bash
cd /opt/syntour
docker-compose -f docker-compose.production.yml down
```

### 重启服务
```bash
cd /opt/syntour
docker-compose -f docker-compose.production.yml restart
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.production.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.production.yml logs -f backend
docker-compose -f docker-compose.production.yml logs -f frontend
docker-compose -f docker-compose.production.yml logs -f postgres
docker-compose -f docker-compose.production.yml logs -f redis
```

### 更新应用
```bash
cd /opt/syntour

# 拉取最新代码
git pull origin master

# 重新构建并启动
docker-compose -f docker-compose.production.yml up --build -d
```

## 数据库管理

### 连接数据库
```bash
# 进入PostgreSQL容器
docker exec -it syntour_postgres psql -U syntour_user -d syntour_db
```

### 数据库备份
```bash
# 创建备份
docker exec syntour_postgres pg_dump -U syntour_user syntour_db > backup_$(date +%Y%m%d_%H%M%S).sql

# 定期备份脚本
echo '0 2 * * * cd /opt/syntour && docker exec syntour_postgres pg_dump -U syntour_user syntour_db > backups/backup_$(date +\%Y\%m\%d_\%H\%M\%S).sql' | crontab -
```

### 数据库恢复
```bash
# 恢复数据库
docker exec -i syntour_postgres psql -U syntour_user -d syntour_db < backup_file.sql
```

## 监控和维护

### 启动监控系统
```bash
# 进入监控目录
cd /opt/syntour/monitoring

# 给启动脚本执行权限
chmod +x start-monitoring.sh

# 启动完整监控系统
./start-monitoring.sh

# 或者手动启动
docker-compose -f docker-compose.monitoring.yml up -d
```

### 监控服务访问
- **Grafana 仪表板**: http://your-server-ip:3000
  - 默认用户名/密码: admin/admin
  - 包含系统、应用、数据库等全方位监控

- **Prometheus 指标**: http://your-server-ip:9090
  - 查看原始指标数据
  - 配置告警规则

- **AlertManager**: http://your-server-ip:9093
  - 管理告警通知
  - 配置邮件/Webhook通知

- **Loki 日志**: 通过 Grafana 访问
  - 集中化日志查询
  - 日志告警配置

### 系统监控指标
- **系统资源**: CPU、内存、磁盘、网络使用率
- **应用性能**: 响应时间、请求量、错误率
- **数据库监控**: 连接数、查询性能、锁等待
- **容器监控**: 容器资源使用、健康状态
- **日志监控**: 错误日志、访问日志、审计日志

### 告警配置
监控系统已预配置以下告警规则：
- CPU使用率 > 80%
- 内存使用率 > 85%
- 磁盘空间 < 10%
- 服务宕机
- 响应时间 > 1秒
- 错误率 > 5%
- 数据库连接数过高
- Redis内存使用率 > 90%

### 系统监控
```bash
# 查看系统资源
htop
df -h
free -h

# 查看Docker资源使用
docker stats

# 清理Docker资源
docker system prune -af
```

### 日志管理
```bash
# 查看应用日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f nginx

# 通过 Loki 查询日志
# 在 Grafana 中使用 LogQL 查询语法
# 例如: {job="syntour-backend"} |= "ERROR"

# 清理日志
docker system prune -f

# 设置日志轮转
cat > /etc/logrotate.d/syntour << EOF
/opt/syntour/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF
```

### 性能优化建议
```bash
# 监控数据库性能
docker-compose exec postgres psql -U syntour_user -d syntour_db -c "SELECT * FROM pg_stat_activity;"

# 监控Redis性能
docker-compose exec redis redis-cli info memory
docker-compose exec redis redis-cli info stats

# 查看容器资源使用
docker stats
```

### 防火墙配置
```bash
# 配置UFW防火墙
ufw allow 22/tcp   # SSH
ufw allow 80/tcp   # HTTP
ufw allow 443/tcp  # HTTPS
ufw --force enable

# 查看防火墙状态
ufw status
```

## SSL证书配置（可选）

### 使用Let's Encrypt
```bash
# 安装Certbot
apt install -y certbot python3-certbot-nginx

# 获取SSL证书
certbot --nginx -d yourdomain.com

# 自动续期
echo '0 12 * * * /usr/bin/certbot renew --quiet' | crontab -
```

## 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :80
   netstat -tulpn | grep :3000
   netstat -tulpn | grep :8000
   
   # 检查Docker服务
   systemctl status docker
   ```

2. **数据库连接失败**
   ```bash
   # 检查PostgreSQL容器
   docker logs syntour_postgres
   
   # 测试数据库连接
   docker exec syntour_postgres pg_isready -U syntour_user
   ```

3. **内存不足**
   ```bash
   # 添加交换空间
   fallocate -l 2G /swapfile
   chmod 600 /swapfile
   mkswap /swapfile
   swapon /swapfile
   echo '/swapfile none swap sw 0 0' >> /etc/fstab
   ```

4. **磁盘空间不足**
   ```bash
   # 清理Docker
   docker system prune -af
   docker volume prune -f
   
   # 清理日志
   journalctl --vacuum-time=7d
   ```

## 性能优化

### 数据库优化
```bash
# 在PostgreSQL容器中执行
docker exec -it syntour_postgres psql -U syntour_user -d syntour_db

-- 创建索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_active ON users(email) WHERE is_active = true;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_conversation_created ON messages(conversation_id, created_at);

-- 分析表统计信息
ANALYZE;
```

### Redis优化
```bash
# 配置Redis内存策略
docker exec syntour_redis redis-cli CONFIG SET maxmemory-policy allkeys-lru
docker exec syntour_redis redis-cli CONFIG SET maxmemory 256mb
```

## 安全建议

1. **更改默认密码**
   - 数据库密码
   - Redis密码
   - JWT密钥
   - 应用密钥

2. **定期更新**
   ```bash
   # 更新系统包
   apt update && apt upgrade -y
   
   # 更新Docker镜像
   docker-compose -f docker-compose.production.yml pull
   docker-compose -f docker-compose.production.yml up -d
   ```

3. **备份策略**
   - 每日数据库备份
   - 每周完整系统备份
   - 异地备份存储

4. **监控告警**
   - 设置资源使用告警
   - 配置服务健康检查
   - 日志异常监控

## 访问地址

部署完成后，可以通过以下地址访问：

- **前端应用**: http://************
- **API文档**: http://************/api/docs
- **健康检查**: http://************/health

## 联系支持

如遇到问题，请检查：
1. 服务日志
2. 系统资源
3. 网络连接
4. 配置文件

更多技术支持，请联系开发团队。