#!/bin/bash

# SynTour 监控系统启动脚本
# 用于启动 Prometheus, <PERSON><PERSON>, Loki, AlertManager 等监控服务

set -e

echo "🚀 启动 SynTour 监控系统..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查 Docker Compose 是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装"
    exit 1
fi

# 创建必要的目录
echo "📁 创建监控数据目录..."
mkdir -p /opt/syntour/monitoring/data/{prometheus,grafana,loki,alertmanager}
mkdir -p /opt/syntour/monitoring/logs
mkdir -p /var/log/syntour

# 设置目录权限
echo "🔐 设置目录权限..."
sudo chown -R 472:472 /opt/syntour/monitoring/data/grafana  # Grafana user
sudo chown -R 65534:65534 /opt/syntour/monitoring/data/prometheus  # nobody user
sudo chown -R 10001:10001 /opt/syntour/monitoring/data/loki  # Loki user
sudo chown -R 65534:65534 /opt/syntour/monitoring/data/alertmanager  # nobody user

# 复制配置文件到正确位置
echo "📋 复制配置文件..."
cp prometheus.yml /opt/syntour/monitoring/
cp alert_rules.yml /opt/syntour/monitoring/
cp alertmanager.yml /opt/syntour/monitoring/
cp loki-config.yml /opt/syntour/monitoring/
cp promtail-config.yml /opt/syntour/monitoring/
cp grafana-dashboard.json /opt/syntour/monitoring/

# 启动监控服务
echo "🔄 启动监控服务..."
docker-compose -f docker-compose.monitoring.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
services=("prometheus" "grafana" "loki" "promtail" "alertmanager" "node-exporter" "cadvisor")

for service in "${services[@]}"; do
    if docker-compose -f docker-compose.monitoring.yml ps | grep -q "$service.*Up"; then
        echo "✅ $service 运行正常"
    else
        echo "❌ $service 启动失败"
        docker-compose -f docker-compose.monitoring.yml logs $service
    fi
done

# 导入 Grafana 仪表板
echo "📊 配置 Grafana 仪表板..."
sleep 10

# 添加 Prometheus 数据源
curl -X POST \
  *********************************/api/datasources \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "Prometheus",
    "type": "prometheus",
    "url": "http://prometheus:9090",
    "access": "proxy",
    "isDefault": true
  }' || echo "⚠️ Prometheus 数据源可能已存在"

# 添加 Loki 数据源
curl -X POST \
  *********************************/api/datasources \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "Loki",
    "type": "loki",
    "url": "http://loki:3100",
    "access": "proxy"
  }' || echo "⚠️ Loki 数据源可能已存在"

# 导入仪表板
curl -X POST \
  *********************************/api/dashboards/db \
  -H 'Content-Type: application/json' \
  -d @grafana-dashboard.json || echo "⚠️ 仪表板可能已存在"

echo ""
echo "🎉 监控系统启动完成！"
echo ""
echo "📊 访问地址："
echo "  - Grafana:     http://localhost:3000 (admin/admin)"
echo "  - Prometheus:  http://localhost:9090"
echo "  - AlertManager: http://localhost:9093"
echo "  - Loki:        http://localhost:3100"
echo ""
echo "📝 日志查看："
echo "  docker-compose -f docker-compose.monitoring.yml logs -f [service_name]"
echo ""
echo "🛑 停止监控："
echo "  docker-compose -f docker-compose.monitoring.yml down"
echo ""
echo "🔄 重启监控："
echo "  docker-compose -f docker-compose.monitoring.yml restart"
echo ""
echo "✨ 监控系统已就绪，开始监控您的 SynTour 应用！"