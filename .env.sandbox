# SynTour沙箱环境配置
# 此文件用于Docker沙箱环境

# === 基础配置 ===
NODE_ENV=development
ENVIRONMENT=sandbox

# === 前端配置 ===
NEXT_PUBLIC_API_URL=http://localhost/api
NEXT_PUBLIC_WS_URL=ws://localhost/ws
NEXT_PUBLIC_APP_NAME=SynTour Sandbox
NEXT_TELEMETRY_DISABLED=1

# === 后端配置 ===
FASTAPI_ENV=development
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true

# === 数据库配置 ===
DATABASE_URL=*******************************************************/syntour_dev
POSTGRES_DB=syntour_dev
POSTGRES_USER=syntour
POSTGRES_PASSWORD=syntour_dev_password
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# === Redis配置 ===
REDIS_URL=redis://redis:6379/0
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# === Google Cloud配置 ===
# 请在实际使用时填入真实值
GOOGLE_CLOUD_PROJECT=your-sandbox-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=/app/backend/config/service-account-key.json
VERTEX_AI_ENDPOINT=your-vertex-ai-endpoint

# === Email配置 - 阿里云邮箱推送服务 ===
EMAIL_HOST=smtpdm.aliyun.com
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=zKE6iQTmcds58cz
EMAIL_FROM=SynText <<EMAIL>>

# === 文件上传配置 ===
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# === 安全配置 ===
SECRET_KEY=sandbox-secret-key-change-in-production
JWT_SECRET=sandbox-jwt-secret-change-in-production
CORS_ORIGINS=http://localhost,http://localhost:3007,http://localhost:80

# === 日志配置 ===
LOG_LEVEL=DEBUG
LOG_FORMAT=detailed

# === 开发工具配置 ===
HOT_RELOAD=true
AUTO_RESTART=true
DEBUG_MODE=true