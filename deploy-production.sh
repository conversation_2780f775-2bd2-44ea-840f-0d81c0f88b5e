#!/bin/bash

# SynTour Production Deployment Script
# Usage: ./deploy-production.sh

set -e  # Exit on any error

echo "🚀 Starting SynTour Production Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root (use sudo)"
    exit 1
fi

# Update system packages
print_status "Updating system packages..."
apt update && apt upgrade -y

# Install Docker if not present
if ! command -v docker &> /dev/null; then
    print_status "Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    usermod -aG docker $USER
    rm get-docker.sh
else
    print_status "Docker is already installed"
fi

# Install Docker Compose if not present
if ! command -v docker-compose &> /dev/null; then
    print_status "Installing Docker Compose..."
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
else
    print_status "Docker Compose is already installed"
fi

# Create application directory
APP_DIR="/opt/syntour"
print_status "Creating application directory: $APP_DIR"
mkdir -p $APP_DIR
cd $APP_DIR

# Clone or update repository
if [ -d ".git" ]; then
    print_status "Updating existing repository..."
    git pull origin master
else
    print_status "Cloning repository..."
    git clone https://github.com/SunflowersLwtech/SynTour.git .
fi

# Copy environment file
if [ ! -f ".env.production" ]; then
    print_warning "Creating .env.production file..."
    cp .env.production.example .env.production 2>/dev/null || {
        print_error ".env.production.example not found. Please create .env.production manually."
        exit 1
    }
    print_warning "Please edit .env.production with your actual configuration before continuing."
    read -p "Press Enter after editing .env.production..."
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p logs nginx/ssl uploads

# Set proper permissions
print_status "Setting proper permissions..."
chown -R 1001:1001 logs uploads
chmod -R 755 logs uploads

# Stop existing containers
print_status "Stopping existing containers..."
docker-compose -f docker-compose.production.yml down --remove-orphans || true

# Remove old images (optional)
read -p "Do you want to remove old Docker images? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Removing old Docker images..."
    docker system prune -af
fi

# Build and start services
print_status "Building and starting services..."
docker-compose -f docker-compose.production.yml up --build -d

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 30

# Check service health
print_status "Checking service health..."
if curl -f http://localhost/health > /dev/null 2>&1; then
    print_status "✅ Application is running successfully!"
    print_status "🌐 Frontend: http://$(curl -s ifconfig.me)"
    print_status "🔧 API: http://$(curl -s ifconfig.me)/api"
else
    print_error "❌ Application health check failed"
    print_status "Checking container logs..."
    docker-compose -f docker-compose.production.yml logs --tail=50
    exit 1
fi

# Setup log rotation
print_status "Setting up log rotation..."
cat > /etc/logrotate.d/syntour << EOF
$APP_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF

# Setup systemd service for auto-start
print_status "Setting up systemd service..."
cat > /etc/systemd/system/syntour.service << EOF
[Unit]
Description=SynTour Application
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$APP_DIR
ExecStart=/usr/local/bin/docker-compose -f docker-compose.production.yml up -d
ExecStop=/usr/local/bin/docker-compose -f docker-compose.production.yml down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable syntour.service

# Setup firewall rules
print_status "Configuring firewall..."
ufw allow 22/tcp   # SSH
ufw allow 80/tcp   # HTTP
ufw allow 443/tcp  # HTTPS
ufw --force enable

print_status "🎉 Deployment completed successfully!"
print_status "📋 Next steps:"
print_status "   1. Configure your domain DNS to point to this server"
print_status "   2. Setup SSL certificates (Let's Encrypt recommended)"
print_status "   3. Configure backup strategy for database"
print_status "   4. Setup monitoring and alerting"
print_status "📊 Monitor logs: docker-compose -f docker-compose.production.yml logs -f"
print_status "🔄 Restart services: systemctl restart syntour"

echo "Deployment completed at $(date)"