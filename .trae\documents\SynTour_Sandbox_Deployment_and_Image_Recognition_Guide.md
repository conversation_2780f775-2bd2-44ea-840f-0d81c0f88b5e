# SynTour 服务器沙箱部署与图片识别功能指南

## 1. 项目概述

SynTour 是一个基于 Next.js 前端和 FastAPI 后端的智能旅游规划应用，集成了先进的多模态AI处理能力，特别是针对马来西亚旅游场景的图片识别和分析功能。

### 1.1 核心功能
- **多模态AI处理**: 支持文本、图片、文件的综合分析
- **智能图片识别**: 马来西亚地标识别、文化元素分析、安全评估
- **旅游智能推荐**: 基于视觉内容的个性化旅游建议
- **实时多语言支持**: 中文、英文、马来文等多语言处理

## 2. 服务器沙箱环境搭建

### 2.1 Docker 容器化部署（推荐）

#### 2.1.1 创建 Dockerfile

**后端 Dockerfile** (`backend/Dockerfile`)
```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非 root 用户
RUN useradd -m -u 1000 syntour && chown -R syntour:syntour /app
USER syntour

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

**前端 Dockerfile** (`frontend/Dockerfile`)
```dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制 package 文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制应用代码
COPY . .

# 构建应用
RUN npm run build

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["npm", "start"]
```

#### 2.1.2 Docker Compose 配置

**docker-compose.yml**
```yaml
version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: syntour-backend
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT}
      - GOOGLE_CLOUD_LOCATION=${GOOGLE_CLOUD_LOCATION}
      - VERTEX_AI_ENDPOINT=${VERTEX_AI_ENDPOINT}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./backend/config:/app/config:ro
      - ./backend/logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - syntour-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: syntour-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://backend:8000
    depends_on:
      - backend
    networks:
      - syntour-network
    restart: unless-stopped

  # PostgreSQL 数据库
  postgres:
    image: postgres:14-alpine
    container_name: syntour-postgres
    environment:
      - POSTGRES_DB=syntour_db
      - POSTGRES_USER=syntour_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - syntour-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: syntour-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - syntour-network
    restart: unless-stopped

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: syntour-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - syntour-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  syntour-network:
    driver: bridge
```

#### 2.1.3 环境变量配置

**生产环境配置** (`.env.production`)
```bash
# 应用环境
ENVIRONMENT=production
NODE_ENV=production

# Google Cloud 配置
GOOGLE_CLOUD_PROJECT=your-production-project-id
GOOGLE_CLOUD_LOCATION=us-central1
VERTEX_AI_ENDPOINT=your-vertex-ai-endpoint
GOOGLE_APPLICATION_CREDENTIALS=/app/config/service-account-key.json
GCS_BUCKET_NAME=syntour-production-storage

# API Keys (使用密钥管理服务)
GOOGLE_SPEECH_API_KEY=${GOOGLE_SPEECH_API_KEY}
GOOGLE_PLACES_API_KEY=${GOOGLE_PLACES_API_KEY}
NEXT_PUBLIC_GOOGLE_MAPS_JAVASCRIPT_API_KEY=${GOOGLE_MAPS_API_KEY}

# 第三方服务 API Keys
AMADEUS_API_KEY=${AMADEUS_API_KEY}
AMADEUS_API_SECRET=${AMADEUS_API_SECRET}
TOMORROW_IO_API_KEY=${TOMORROW_IO_API_KEY}
FLIGHT_API_KEY=${FLIGHT_API_KEY}
GEOAPIFY_API_KEY=${GEOAPIFY_API_KEY}
HOTELBEDS_API_KEY=${HOTELBEDS_API_KEY}
HOTELBEDS_SECRET=${HOTELBEDS_SECRET}
OPEN_WEATHER_MAP_API_KEY=${OPENWEATHER_API_KEY}

# 数据库配置
DATABASE_URL=postgresql://syntour_user:${DB_PASSWORD}@postgres:5432/syntour_db
DB_PASSWORD=${DB_PASSWORD}

# Redis 配置
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
REDIS_PASSWORD=${REDIS_PASSWORD}

# 安全配置
SECRET_KEY=${SECRET_KEY}
ALLOWED_HOSTS=yourdomain.com,api.yourdomain.com
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# 性能配置
MAX_CONCURRENT_TASKS=20
HEALTH_CHECK_INTERVAL=30.0
HTTP_POOL_MAX_SIZE=50
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60
CACHE_TTL_SECONDS=300

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=50MB
LOG_BACKUP_COUNT=10
```

### 2.2 虚拟环境隔离部署

#### 2.2.1 系统级隔离

**创建专用用户和目录**
```bash
#!/bin/bash
# 系统隔离部署脚本

# 创建专用用户
sudo useradd -m -s /bin/bash syntour
sudo usermod -aG docker syntour  # 如果使用 Docker

# 创建应用目录
sudo mkdir -p /opt/syntour
sudo chown syntour:syntour /opt/syntour

# 设置目录权限
sudo chmod 755 /opt/syntour

# 创建日志目录
sudo mkdir -p /var/log/syntour
sudo chown syntour:syntour /var/log/syntour

# 创建配置目录
sudo mkdir -p /etc/syntour
sudo chown syntour:syntour /etc/syntour
```

#### 2.2.2 Python 虚拟环境

**后端环境设置**
```bash
#!/bin/bash
# 后端虚拟环境设置

cd /opt/syntour

# 创建 Python 虚拟环境
python3 -m venv venv
source venv/bin/activate

# 升级 pip
pip install --upgrade pip

# 安装依赖
pip install -r backend/requirements.txt

# 设置环境变量
cp .env.production .env

# 创建启动脚本
cat > start_backend.sh << 'EOF'
#!/bin/bash
cd /opt/syntour
source venv/bin/activate
export PYTHONPATH=/opt/syntour/backend:$PYTHONPATH
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
EOF

chmod +x start_backend.sh
```

#### 2.2.3 Node.js 环境隔离

**前端环境设置**
```bash
#!/bin/bash
# 前端环境设置

cd /opt/syntour/frontend

# 使用 nvm 管理 Node.js 版本
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc

# 安装指定版本的 Node.js
nvm install 18
nvm use 18

# 安装依赖
npm ci --production

# 构建应用
npm run build

# 创建启动脚本
cat > start_frontend.sh << 'EOF'
#!/bin/bash
cd /opt/syntour/frontend
source ~/.bashrc
nvm use 18
npm start
EOF

chmod +x start_frontend.sh
```

### 2.3 安全配置

#### 2.3.1 防火墙设置

```bash
#!/bin/bash
# 防火墙配置脚本

# 启用 UFW
sudo ufw --force enable

# 默认策略
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许 SSH
sudo ufw allow ssh

# 允许 HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 限制应用端口访问（仅本地）
sudo ufw allow from 127.0.0.1 to any port 8000  # 后端
sudo ufw allow from 127.0.0.1 to any port 3000  # 前端
sudo ufw allow from 127.0.0.1 to any port 5432  # PostgreSQL
sudo ufw allow from 127.0.0.1 to any port 6379  # Redis

# 显示状态
sudo ufw status verbose
```

#### 2.3.2 SSL/TLS 配置

**Let's Encrypt 证书**
```bash
#!/bin/bash
# SSL 证书配置

# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

#### 2.3.3 容器安全

**Docker 安全配置**
```yaml
# docker-compose.security.yml
version: '3.8'

services:
  backend:
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/tmp
    volumes:
      - ./backend/logs:/app/logs:rw
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    user: "1000:1000"
    
  frontend:
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/tmp
    cap_drop:
      - ALL
    user: "1001:1001"
```

## 3. 图片识别功能详解

### 3.1 多模态处理系统架构

```mermaid
graph TD
    A[用户上传图片] --> B[MultimodalProcessor]
    B --> C[图片预处理]
    C --> D[地标识别]
    C --> E[文化元素分析]
    C --> F[安全评估]
    C --> G[构图分析]
    D --> H[综合分析]
    E --> H
    F --> H
    G --> H
    H --> I[旅游建议生成]
    I --> J[返回结构化结果]
```

### 3.2 核心功能模块

#### 3.2.1 地标识别系统

**支持的马来西亚地标**
- **双子塔 (Petronas Twin Towers)**: 吉隆坡标志性建筑
- **黑风洞 (Batu Caves)**: 雪兰莪州印度教圣地
- **槟城山 (Penang Hill)**: 槟城历史山站
- **神山 (Mount Kinabalu)**: 沙巴州最高峰

**识别能力**
```python
# 地标识别示例
landmark_detection = {
    "landmarks": [
        {
            "name": "Petronas Twin Towers",
            "location": "Kuala Lumpur",
            "confidence": 0.95,
            "description": "Iconic twin skyscrapers and symbol of Malaysia",
            "visit_tips": [
                "Best viewed at night",
                "Visit Skybridge and observation deck",
                "KLCC Park for photos"
            ]
        }
    ],
    "location_suggestions": [
        "KLCC Shopping Centre",
        "Suria KLCC",
        "Aquaria KLCC"
    ]
}
```

#### 3.2.2 文化元素分析

**分析维度**
- **建筑风格**: 清真寺、寺庙、殖民地建筑、传统建筑
- **美食文化**: 街头小食、餐厅、本地菜肴、清真食品
- **服饰文化**: 传统服装、文化服饰
- **活动庆典**: 节庆、仪式、市场、文化活动
- **宗教符号**: 宗教标志、文化工艺品

**文化指导建议**
```python
# 文化分析示例
cultural_analysis = {
    "detected_elements": {
        "architecture": ["mosque", "traditional"],
        "activities": ["cultural event"]
    },
    "cultural_tips": [
        "Dress modestly when visiting religious sites",
        "Remove shoes before entering",
        "Respect prayer times and ceremonies",
        "Ask permission before photographing people"
    ],
    "sensitivity_level": "high",
    "recommended_behavior": [
        "Show respect for local customs",
        "Learn basic Malay greetings",
        "Understand religious practices"
    ]
}
```

#### 3.2.3 安全评估系统

**风险等级评估**
- **高风险**: 悬崖、陡峭地形、危险区域、限制区域
- **中等风险**: 拥挤场所、交通繁忙、施工区域、夜间活动
- **低风险**: 公园、博物馆、购物中心、酒店、餐厅

**安全建议生成**
```python
# 安全评估示例
safety_assessment = {
    "risk_level": "medium",
    "safety_concerns": ["Moderate caution advised"],
    "recommendations": [
        "Stay aware of surroundings",
        "Keep valuables secure",
        "Travel in groups when possible"
    ],
    "emergency_info": {
        "police": "999",
        "ambulance": "999",
        "fire": "994",
        "tourist_hotline": "1-300-88-5050"
    }
}
```

### 3.3 API 接口说明

#### 3.3.1 多模态分析接口

**端点**: `POST /api/ai/multimodal`

**请求参数**
```python
# 请求格式
{
    "message": "请分析这张图片中的马来西亚景点",
    "context": "我计划去吉隆坡旅游",
    "files": ["image_file_1.jpg", "image_file_2.jpg"]
}
```

**响应格式**
```python
# 响应示例
{
    "response": "这是吉隆坡的双子塔，马来西亚的标志性建筑...",
    "analysis_results": {
        "visual_analysis": {
            "image_0": {
                "landmarks": [...],
                "cultural_elements": {...},
                "safety_assessment": {...},
                "travel_relevance_score": 0.95
            }
        },
        "integrated_insights": {
            "visual_insights": [
                "Iconic Malaysian landmark detected",
                "Urban tourism destination",
                "Photography-friendly location"
            ],
            "travel_recommendations": [
                "Visit during sunset for best photos",
                "Book Skybridge tickets in advance",
                "Explore nearby KLCC area"
            ]
        }
    },
    "processing_metadata": {
        "processed_items": 1,
        "success_rate": 1.0,
        "processing_time": 2.34
    }
}
```

#### 3.3.2 图片上传接口

**前端上传配置**
```typescript
// 图片上传配置
export const CHAT_UPLOAD_CONFIG = {
  ALLOWED_IMAGE_TYPES: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ],
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_FILES: 5,
  COMPRESSION_QUALITY: 0.8,
  MAX_DIMENSION: 2048
};
```

**上传处理流程**
```typescript
// 图片处理服务
class ImageUploadService {
  static async processImageUpload(file: File): Promise<Attachment> {
    // 1. 验证文件类型和大小
    this.validateFile(file);
    
    // 2. 压缩图片
    const compressedFile = await this.compressImage(file);
    
    // 3. 生成预览
    const previewUrl = await this.generatePreview(compressedFile);
    
    // 4. 上传到服务器
    const uploadResult = await this.uploadToAPI(compressedFile);
    
    return {
      id: generateId(),
      name: file.name,
      type: 'image',
      size: compressedFile.size,
      previewUrl,
      uploadUrl: uploadResult.url
    };
  }
}
```

### 3.4 Google Cloud 集成

#### 3.4.1 服务账户配置

**创建服务账户**
```bash
# 创建服务账户
gcloud iam service-accounts create syntour-service-account \
    --description="SynTour application service account" \
    --display-name="SynTour Service Account"

# 分配权限
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:syntour-service-account@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/aiplatform.user"

gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:syntour-service-account@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.objectAdmin"

# 生成密钥文件
gcloud iam service-accounts keys create service-account-key.json \
    --iam-account=syntour-service-account@PROJECT_ID.iam.gserviceaccount.com
```

#### 3.4.2 Vertex AI 配置

**模型端点设置**
```python
# Google Cloud 配置
GOOGLE_CLOUD_CONFIG = {
    "project_id": "your-project-id",
    "location": "us-central1",
    "vertex_ai_endpoint": "projects/your-project/locations/us-central1/endpoints/your-endpoint",
    "model_name": "gemini-pro-vision",
    "credentials_path": "/app/config/service-account-key.json"
}
```

## 4. 部署步骤

### 4.1 快速部署脚本

**一键部署脚本** (`deploy-sandbox.sh`)
```bash
#!/bin/bash
set -e

echo "🚀 开始部署 SynTour 沙箱环境..."

# 检查依赖
command -v docker >/dev/null 2>&1 || { echo "❌ Docker 未安装"; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { echo "❌ Docker Compose 未安装"; exit 1; }

# 创建必要目录
mkdir -p logs config ssl

# 检查环境变量文件
if [ ! -f .env.production ]; then
    echo "❌ 请先配置 .env.production 文件"
    exit 1
fi

# 检查 Google Cloud 服务账户密钥
if [ ! -f config/service-account-key.json ]; then
    echo "❌ 请将 Google Cloud 服务账户密钥放置在 config/service-account-key.json"
    exit 1
fi

# 构建并启动服务
echo "📦 构建 Docker 镜像..."
docker-compose -f docker-compose.yml -f docker-compose.security.yml build

echo "🚀 启动服务..."
docker-compose -f docker-compose.yml -f docker-compose.security.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 健康检查
echo "🏥 执行健康检查..."
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 后端服务正常"
else
    echo "❌ 后端服务异常"
    docker-compose logs backend
    exit 1
fi

if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常"
    docker-compose logs frontend
    exit 1
fi

echo "🎉 部署完成！"
echo "🌐 前端访问: http://localhost:3000"
echo "🔧 后端 API: http://localhost:8000"
echo "📚 API 文档: http://localhost:8000/docs"
echo "📊 健康检查: http://localhost:8000/health"
```

### 4.2 监控和维护

#### 4.2.1 健康检查脚本

```bash
#!/bin/bash
# 健康检查脚本 (health-check.sh)

LOG_FILE="/var/log/syntour/health-check.log"
ALERT_EMAIL="<EMAIL>"

# 检查容器状态
check_container() {
    local container=$1
    if ! docker ps | grep -q $container; then
        echo "$(date): Container $container is down" >> $LOG_FILE
        echo "Container $container is down" | mail -s "SynTour Alert" $ALERT_EMAIL
        return 1
    fi
    return 0
}

# 检查 API 响应
check_api() {
    local url=$1
    local expected_code=$2
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" $url)
    
    if [ "$response_code" != "$expected_code" ]; then
        echo "$(date): $url returned $response_code" >> $LOG_FILE
        return 1
    fi
    return 0
}

# 执行检查
check_container "syntour-backend"
check_container "syntour-frontend"
check_container "syntour-postgres"
check_container "syntour-redis"

check_api "http://localhost:8000/health" "200"
check_api "http://localhost:3000" "200"

echo "$(date): Health check completed" >> $LOG_FILE
```

#### 4.2.2 日志管理

**日志轮转配置** (`/etc/logrotate.d/syntour`)
```
/opt/syntour/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 syntour syntour
    postrotate
        docker-compose restart backend frontend
    endscript
}
```

## 5. 使用示例

### 5.1 图片识别 API 调用

**Python 客户端示例**
```python
import requests
import base64

# 图片识别 API 调用
def analyze_image(image_path, message, context=None):
    url = "http://localhost:8000/api/ai/multimodal"
    
    # 准备文件
    with open(image_path, 'rb') as f:
        files = {'files': f}
        data = {
            'message': message,
            'context': context or ''
        }
        
        response = requests.post(url, files=files, data=data)
        return response.json()

# 使用示例
result = analyze_image(
    'petronas_towers.jpg',
    '请分析这张图片中的建筑',
    '我计划去吉隆坡旅游'
)

print(f"识别结果: {result['response']}")
print(f"地标信息: {result['analysis_results']['visual_analysis']}")
```

**JavaScript 客户端示例**
```javascript
// 前端图片上传和分析
class ImageAnalyzer {
  static async analyzeImage(file, message, context) {
    const formData = new FormData();
    formData.append('files', file);
    formData.append('message', message);
    formData.append('context', context || '');
    
    try {
      const response = await fetch('/api/ai/multimodal', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Image analysis failed:', error);
      throw error;
    }
  }
}

// 使用示例
const fileInput = document.getElementById('imageInput');
fileInput.addEventListener('change', async (event) => {
  const file = event.target.files[0];
  if (file) {
    try {
      const result = await ImageAnalyzer.analyzeImage(
        file,
        '请分析这张图片',
        '马来西亚旅游规划'
      );
      
      console.log('分析结果:', result);
      displayResults(result);
    } catch (error) {
      console.error('分析失败:', error);
    }
  }
});
```

### 5.2 批量图片处理

```python
# 批量图片分析
import asyncio
import aiohttp
import os

async def batch_analyze_images(image_folder, message):
    """批量分析文件夹中的图片"""
    
    async with aiohttp.ClientSession() as session:
        tasks = []
        
        for filename in os.listdir(image_folder):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                image_path = os.path.join(image_folder, filename)
                task = analyze_single_image(session, image_path, message)
                tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results

async def analyze_single_image(session, image_path, message):
    """分析单张图片"""
    url = "http://localhost:8000/api/ai/multimodal"
    
    with open(image_path, 'rb') as f:
        data = aiohttp.FormData()
        data.add_field('files', f, filename=os.path.basename(image_path))
        data.add_field('message', message)
        
        async with session.post(url, data=data) as response:
            if response.status == 200:
                result = await response.json()
                return {
                    'filename': os.path.basename(image_path),
                    'analysis': result
                }
            else:
                return {
                    'filename': os.path.basename(image_path),
                    'error': f'HTTP {response.status}'
                }

# 使用示例
if __name__ == "__main__":
    results = asyncio.run(batch_analyze_images(
        '/path/to/images',
        '请识别这些马来西亚旅游景点'
    ))
    
    for result in results:
        if 'error' not in result:
            print(f"文件: {result['filename']}")
            print(f"分析: {result['analysis']['response']}")
            print("-" * 50)
```

## 6. 故障排除

### 6.1 常见问题

#### 6.1.1 Google Cloud 认证问题

**问题**: `google.auth.exceptions.DefaultCredentialsError`

**解决方案**:
```bash
# 检查服务账户密钥文件
ls -la config/service-account-key.json

# 验证环境变量
echo $GOOGLE_APPLICATION_CREDENTIALS

# 测试认证
gcloud auth activate-service-account --key-file=config/service-account-key.json
gcloud auth list
```

#### 6.1.2 图片上传失败

**问题**: 图片上传超时或失败

**解决方案**:
```python
# 检查文件大小限制
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

# 增加超时时间
REQUEST_TIMEOUT = 60  # 60秒

# 启用图片压缩
COMPRESSION_ENABLED = True
COMPRESSION_QUALITY = 0.8
```

#### 6.1.3 容器启动失败

**问题**: Docker 容器无法启动

**解决方案**:
```bash
# 查看容器日志
docker-compose logs backend
docker-compose logs frontend

# 检查端口占用
netstat -tlnp | grep :8000
netstat -tlnp | grep :3000

# 重新构建镜像
docker-compose build --no-cache
docker-compose up -d
```

### 6.2 性能优化

#### 6.2.1 图片处理优化

```python
# 图片处理优化配置
IMAGE_PROCESSING_CONFIG = {
    "max_concurrent_uploads": 5,
    "compression_quality": 0.8,
    "max_dimension": 2048,
    "cache_processed_images": True,
    "cache_ttl": 3600,  # 1小时
    "use_webp_format": True,
    "enable_lazy_loading": True
}
```

#### 6.2.2 API 响应优化

```python
# API 响应缓存
from functools import lru_cache

@lru_cache(maxsize=1000)
def get_landmark_info(landmark_name):
    """缓存地标信息"""
    return landmark_database.get(landmark_name)

# 异步处理优化
async def process_multiple_images_optimized(images):
    """优化的批量图片处理"""
    semaphore = asyncio.Semaphore(5)  # 限制并发数
    
    async def process_with_semaphore(image):
        async with semaphore:
            return await process_single_image(image)
    
    tasks = [process_with_semaphore(img) for img in images]
    return await asyncio.gather(*tasks)
```

## 7. 总结

本指南提供了 SynTour 项目的完整沙箱部署方案和图片识别功能说明。通过 Docker 容器化部署，您可以快速创建一个安全、隔离的运行环境。项目的多模态AI处理系统能够智能识别马来西亚旅游景点，提供文化指导和安全建议，为用户提供个性化的旅游规划服务。

### 关键特性
- ✅ **完整的容器化部署方案**
- ✅ **先进的图片识别和分析能力**
- ✅ **马来西亚旅游专业知识库**
- ✅ **多层安全防护机制**
- ✅ **实时健康监控和故障恢复**
- ✅ **高性能异步处理架构**

按照本指南的步骤，您可以快速部署一个功能完整、安全可靠的 SynTour 智能旅游规划系统。