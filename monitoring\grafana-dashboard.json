{"dashboard": {"id": null, "title": "SynTour Production Monitoring", "tags": ["syntour", "production"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "System Overview", "type": "stat", "targets": [{"expr": "up", "legendFormat": "Services Up"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "CPU Usage", "type": "graph", "targets": [{"expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "CPU Usage %"}], "yAxes": [{"min": 0, "max": 100, "unit": "percent"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100", "legendFormat": "Memory Usage %"}], "yAxes": [{"min": 0, "max": 100, "unit": "percent"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Disk Usage", "type": "graph", "targets": [{"expr": "(node_filesystem_size_bytes{mountpoint=\"/\"} - node_filesystem_avail_bytes{mountpoint=\"/\"}) / node_filesystem_size_bytes{mountpoint=\"/\"} * 100", "legendFormat": "Disk Usage %"}], "yAxes": [{"min": 0, "max": 100, "unit": "percent"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "HTTP Request Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "{{method}} {{status}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "Response Time", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile"}], "yAxes": [{"unit": "s"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 7, "title": "Database Connections", "type": "graph", "targets": [{"expr": "pg_stat_database_numbackends", "legendFormat": "Active Connections"}, {"expr": "pg_settings_max_connections", "legendFormat": "Max Connections"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 8, "title": "Redis Memory Usage", "type": "graph", "targets": [{"expr": "redis_memory_used_bytes", "legendFormat": "Used Memory"}, {"expr": "redis_memory_max_bytes", "legendFormat": "Max Memory"}], "yAxes": [{"unit": "bytes"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 9, "title": "Container CPU Usage", "type": "graph", "targets": [{"expr": "sum(rate(container_cpu_usage_seconds_total{name!=\"\"}[3m])) BY (name) * 100", "legendFormat": "{{name}}"}], "yAxes": [{"unit": "percent"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 10, "title": "Container Memory Usage", "type": "graph", "targets": [{"expr": "sum(container_memory_working_set_bytes{name!=\"\"}) BY (name)", "legendFormat": "{{name}}"}], "yAxes": [{"unit": "bytes"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}}