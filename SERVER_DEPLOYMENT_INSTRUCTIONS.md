# SynTour 服务器部署指令

## 🚀 快速部署指南

### 前置条件
- 服务器IP: ************
- 项目路径: /opt/syntour
- 确保Docker和Docker Compose已安装

### 1. 连接服务器
```bash
ssh root@************
# 密码: Lw20060607.@@
```

### 2. 进入项目目录
```bash
cd /opt/syntour
```

### 3. 配置环境变量
```bash
# 复制环境变量模板
cp .env.production.example .env.production

# 编辑环境变量文件
nano .env.production
```

**必须设置的环境变量:**
```bash
POSTGRES_PASSWORD=your_secure_postgres_password
REDIS_PASSWORD=your_secure_redis_password  
JWT_SECRET=your_very_secure_jwt_secret_key_at_least_32_characters
```

### 4. 运行部署测试
```bash
# 给脚本执行权限
chmod +x test-deployment.sh

# 运行测试
./test-deployment.sh
```

### 5. 执行部署
```bash
# 给脚本执行权限
chmod +x deploy-fix.sh

# 运行部署
./deploy-fix.sh
```

## 🔍 故障排查

### 检查服务状态
```bash
# 查看所有容器状态
docker-compose -f docker-compose.production.yml ps

# 查看特定服务日志
docker-compose -f docker-compose.production.yml logs backend
docker-compose -f docker-compose.production.yml logs frontend
docker-compose -f docker-compose.production.yml logs postgres
```

### 健康检查
```bash
# 检查后端API
curl http://localhost:8000/health

# 检查前端
curl http://localhost:3000

# 检查数据库连接
docker exec syntour_postgres pg_isready -U syntour_user -d syntour_db
```

### 重启服务
```bash
# 重启所有服务
docker-compose -f docker-compose.production.yml restart

# 重启特定服务
docker-compose -f docker-compose.production.yml restart backend
```

## 📊 监控命令

### 实时日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.production.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.production.yml logs -f backend
```

### 资源使用
```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
df -h

# 查看内存使用
free -h
```

## 🔧 维护操作

### 更新代码
```bash
# 拉取最新代码
git pull origin main

# 重新构建并启动
docker-compose -f docker-compose.production.yml up --build -d
```

### 清理系统
```bash
# 清理未使用的Docker资源
docker system prune -f

# 清理未使用的镜像
docker image prune -f
```

### 备份数据库
```bash
# 创建数据库备份
docker exec syntour_postgres pg_dump -U syntour_user syntour_db > backup_$(date +%Y%m%d_%H%M%S).sql
```

## 🌐 访问地址

部署成功后，可以通过以下地址访问：

- **前端应用**: http://************
- **API接口**: http://************/api
- **健康检查**: http://************/health

## ⚠️ 注意事项

1. **环境变量安全**: 确保.env.production文件中的密码足够复杂
2. **防火墙配置**: 确保必要端口已开放
3. **SSL证书**: 生产环境建议配置HTTPS
4. **定期备份**: 建议定期备份数据库和重要文件
5. **监控告警**: 建议配置监控和告警系统

## 📞 支持

如遇问题，请检查：
1. 服务器资源是否充足
2. 网络连接是否正常
3. 环境变量是否正确配置
4. Docker服务是否正常运行
