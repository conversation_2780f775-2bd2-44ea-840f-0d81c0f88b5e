# SynTour 修复文件上传脚本 (PowerShell版本)

$SERVER = "************"
$USER = "root"
$REMOTE_PATH = "/opt/syntour"

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "SynTour 修复文件上传脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "正在上传修复后的文件到服务器..." -ForegroundColor Yellow

# 检查必要文件是否存在
$files = @(
    "Dockerfile.backend",
    "docker-compose.production.yml", 
    "backend/requirements-core.txt",
    "deploy-fix.sh",
    "test-deployment.sh",
    ".env.production.example",
    "SERVER_DEPLOYMENT_INSTRUCTIONS.md"
)

foreach ($file in $files) {
    if (!(Test-Path $file)) {
        Write-Host "错误: 文件 $file 不存在" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "1. 上传 Dockerfile.backend" -ForegroundColor Green
scp Dockerfile.backend "${USER}@${SERVER}:${REMOTE_PATH}/"

Write-Host ""
Write-Host "2. 上传 docker-compose.production.yml" -ForegroundColor Green
scp docker-compose.production.yml "${USER}@${SERVER}:${REMOTE_PATH}/"

Write-Host ""
Write-Host "3. 上传 backend/requirements-core.txt" -ForegroundColor Green
scp backend/requirements-core.txt "${USER}@${SERVER}:${REMOTE_PATH}/backend/"

Write-Host ""
Write-Host "4. 上传部署脚本" -ForegroundColor Green
scp deploy-fix.sh "${USER}@${SERVER}:${REMOTE_PATH}/"
scp test-deployment.sh "${USER}@${SERVER}:${REMOTE_PATH}/"
scp server-deploy-complete.sh "${USER}@${SERVER}:${REMOTE_PATH}/"

Write-Host ""
Write-Host "5. 上传环境变量模板" -ForegroundColor Green
scp .env.production.example "${USER}@${SERVER}:${REMOTE_PATH}/"

Write-Host ""
Write-Host "6. 上传部署指南" -ForegroundColor Green
scp SERVER_DEPLOYMENT_INSTRUCTIONS.md "${USER}@${SERVER}:${REMOTE_PATH}/"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "文件上传完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Yellow
Write-Host "1. SSH连接到服务器: ssh ${USER}@${SERVER}" -ForegroundColor White
Write-Host "2. 进入项目目录: cd ${REMOTE_PATH}" -ForegroundColor White
Write-Host "3. 运行完整部署: chmod +x server-deploy-complete.sh" -ForegroundColor White
Write-Host "4. 然后执行: ./server-deploy-complete.sh" -ForegroundColor White

Write-Host ""
Write-Host "按任意键继续..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
