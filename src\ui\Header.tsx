"use client";
import { useState, useRef, useEffect } from "react";
import { User, Settings, LogOut, UserCircle, Bell, Shield, Database, Clock, Link, Key, ChevronDown, X } from "lucide-react";
import Brand from "./Brand";
import SubscribeModal from "./SubscribeModal";

export default function Header() {
  const [showSubscribeModal, setShowSubscribeModal] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showSettingsPanel, setShowSettingsPanel] = useState(false);
  const [activeSettingsTab, setActiveSettingsTab] = useState("general");
  const userMenuRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭用户菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    }

    if (showUserMenu) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showUserMenu]);

  // 埋点函数
  const trackSubscribeClick = () => {
    console.log('Subscribe button clicked from header');
    setShowSubscribeModal(true);
  };

  const handleLogout = () => {
    // TODO: 实现登出逻辑
    console.log('User logged out');
    setShowUserMenu(false);
  };

  const settingsTabs = [
    { id: "general", label: "General", icon: Settings },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "personalization", label: "Personalization", icon: Clock },
    { id: "connectors", label: "Connectors", icon: Link },
    { id: "schedules", label: "Schedules", icon: Clock },
    { id: "data-controls", label: "Data controls", icon: Database },
    { id: "security", label: "Security", icon: Shield },
    { id: "account", label: "Account", icon: UserCircle },
  ];

  return (
    <>
      <header className="w-full bg-white sticky top-0 z-50 border-b border-gray-100">
        <div className="w-full px-8">
          <div className="flex items-center justify-between py-3">
            {/* 左侧 - 用户个人主页管理 */}
            <div className="flex-1 flex items-center">
              <div className="relative" ref={userMenuRef}>
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center gap-3 p-2 rounded-xl hover:bg-gray-50 transition-colors duration-200 group"
                  aria-label="User menu"
                >
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-my-blue-500 to-my-gold-500 flex items-center justify-center text-white font-semibold text-lg shadow-lg">
                    <User className="w-5 h-5" />
                  </div>
                  <div className="hidden md:block text-left">
                    <div className="font-semibold text-gray-900 text-sm">John Doe</div>
                    <div className="text-xs text-gray-500">Premium Member</div>
                  </div>
                  <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${showUserMenu ? 'rotate-180' : ''}`} />
                </button>

                {/* 用户下拉菜单 */}
                {showUserMenu && (
                  <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-2xl shadow-2xl border border-gray-200 py-2 z-50">
                    <div className="px-4 py-3 border-b border-gray-100">
                      <div className="font-semibold text-gray-900">John Doe</div>
                      <div className="text-sm text-gray-500"><EMAIL></div>
                    </div>
                    
                    <div className="py-2">
                      <button
                        onClick={() => {
                          setShowSettingsPanel(true);
                          setShowUserMenu(false);
                        }}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-3"
                      >
                        <Settings className="w-4 h-4" />
                        Settings & Preferences
                      </button>
                      <button
                        onClick={handleLogout}
                        className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-3"
                      >
                        <LogOut className="w-4 h-4" />
                        Sign Out
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            {/* 居中的Logo和品牌名 */}
            <div className="flex items-center">
              <Brand />
            </div>
            
            {/* 右侧区域 - 订阅按钮 */}
            <div className="flex-1 flex justify-end">
              <button
                onClick={trackSubscribeClick}
                className="inline-flex items-center justify-center gap-2 px-4 py-2.5 rounded-full bg-gradient-to-r from-myrNavy to-myrYellow text-white font-semibold text-xs sm:text-sm hover:scale-105 active:scale-95 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-myrYellow/50 focus:ring-offset-2 transition-all duration-200 min-h-[44px]"
                aria-label="Get Free Malaysia Travel Guide"
              >
                <span className="text-base">✨</span>
                <span className="hidden md:inline">Subscribe & Get Free Malaysia Travel Guide</span>
                <span className="hidden sm:inline md:hidden">Free Malaysia Guide</span>
                <span className="sm:hidden">Free Guide</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 设置面板 */}
      {showSettingsPanel && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] flex items-center justify-center p-4">
          <div className="bg-white rounded-3xl shadow-2xl w-full max-w-4xl h-[80vh] flex overflow-hidden">
            {/* 左侧导航 */}
            <div className="w-64 bg-gray-50 p-6 border-r border-gray-200">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Settings</h2>
                <button
                  onClick={() => setShowSettingsPanel(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              <nav className="space-y-1">
                {settingsTabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveSettingsTab(tab.id)}
                      className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-xl text-left transition-all duration-200 ${
                        activeSettingsTab === tab.id
                          ? 'bg-white text-my-blue-600 shadow-sm border border-gray-200'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      <span className="text-sm font-medium">{tab.label}</span>
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* 右侧内容区域 */}
            <div className="flex-1 p-6 overflow-y-auto">
              <div className="max-w-2xl">
                <h3 className="text-2xl font-bold text-gray-900 mb-6 capitalize">
                  {settingsTabs.find(tab => tab.id === activeSettingsTab)?.label}
                </h3>
                
                {/* 设置内容 */}
                <div className="space-y-6">
                  {activeSettingsTab === "general" && (
                    <>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between py-3 border-b border-gray-100">
                          <div>
                            <div className="font-medium text-gray-900">Theme</div>
                            <div className="text-sm text-gray-500">Choose your preferred appearance</div>
                          </div>
                          <select className="px-3 py-2 border border-gray-200 rounded-lg text-sm">
                            <option>System</option>
                            <option>Light</option>
                            <option>Dark</option>
                          </select>
                        </div>
                        
                        <div className="flex items-center justify-between py-3 border-b border-gray-100">
                          <div>
                            <div className="font-medium text-gray-900">Language</div>
                            <div className="text-sm text-gray-500">Select your preferred language</div>
                          </div>
                          <select className="px-3 py-2 border border-gray-200 rounded-lg text-sm">
                            <option>Auto-detect</option>
                            <option>English</option>
                            <option>中文</option>
                            <option>Bahasa Malaysia</option>
                          </select>
                        </div>
                        
                        <div className="flex items-center justify-between py-3 border-b border-gray-100">
                          <div>
                            <div className="font-medium text-gray-900">Notifications</div>
                            <div className="text-sm text-gray-500">Manage your notification preferences</div>
                          </div>
                          <div className="w-12 h-6 bg-my-blue-500 rounded-full relative cursor-pointer">
                            <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 transition-transform duration-200"></div>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                  
                  {activeSettingsTab === "account" && (
                    <div className="space-y-4">
                      <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
                        <div className="flex items-center gap-3">
                          <UserCircle className="w-5 h-5 text-blue-600" />
                          <div>
                            <div className="font-medium text-blue-900">Account Information</div>
                            <div className="text-sm text-blue-700">Manage your personal details and preferences</div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                          <input type="text" className="w-full px-3 py-2 border border-gray-200 rounded-lg" defaultValue="John" />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                          <input type="text" className="w-full px-3 py-2 border border-gray-200 rounded-lg" defaultValue="Doe" />
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" className="w-full px-3 py-2 border border-gray-200 rounded-lg" defaultValue="<EMAIL>" />
                      </div>
                    </div>
                  )}
                  
                  {activeSettingsTab === "security" && (
                    <div className="space-y-4">
                      <div className="p-4 bg-green-50 rounded-xl border border-green-200">
                        <div className="flex items-center gap-3">
                          <Shield className="w-5 h-5 text-green-600" />
                          <div>
                            <div className="font-medium text-green-900">Security Status</div>
                            <div className="text-sm text-green-700">Your account is secure with 2FA enabled</div>
                          </div>
                        </div>
                      </div>
                      
                      <button className="w-full px-4 py-3 border border-gray-200 rounded-lg text-left hover:bg-gray-50 transition-colors">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-gray-900">Two-Factor Authentication</div>
                            <div className="text-sm text-gray-500">Add an extra layer of security</div>
                          </div>
                          <div className="w-12 h-6 bg-green-500 rounded-full relative">
                            <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 left-0.5 transition-transform duration-200"></div>
                          </div>
                        </div>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 订阅模态框 */}
      <SubscribeModal 
        isOpen={showSubscribeModal} 
        onClose={() => setShowSubscribeModal(false)} 
      />
    </>
  );
}
