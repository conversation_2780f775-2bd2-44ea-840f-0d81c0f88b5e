from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import secrets
import hashlib
import jwt
from passlib.context import Crypt<PERSON>ontext
from sqlalchemy.orm import Session
from email_validator import validate_email, EmailNotValidError
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class AuthService:
    """认证服务类"""
    
    def __init__(self, secret_key: str = "your-secret-key"):
        self.secret_key = secret_key
        self.algorithm = "HS256"
        self.access_token_expire_minutes = 30
        self.refresh_token_expire_days = 7
        
        # 存储验证码和重置令牌（生产环境应使用Redis）
        self.verification_codes: Dict[str, Dict[str, Any]] = {}
        self.reset_tokens: Dict[str, Dict[str, Any]] = {}
    
    def validate_email_format(self, email: str) -> bool:
        """验证邮箱格式"""
        try:
            validate_email(email)
            return True
        except EmailNotValidError:
            return False
    
    def hash_password(self, password: str) -> str:
        """加密密码"""
        return pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def generate_verification_code(self, email: str, purpose: str = "login") -> str:
        """生成验证码"""
        # 生成6位数字验证码
        code = ''.join([str(secrets.randbelow(10)) for _ in range(6)])
        
        # 存储验证码（5分钟有效期）
        self.verification_codes[f"{email}:{purpose}"] = {
            'code': code,
            'email': email,
            'purpose': purpose,
            'created_at': datetime.utcnow(),
            'expires_at': datetime.utcnow() + timedelta(minutes=5),
            'attempts': 0,
            'max_attempts': 3
        }
        
        logger.info(f"为邮箱 {email} 生成了{purpose}验证码")
        return code
    
    def verify_verification_code(self, email: str, code: str, purpose: str = "login") -> Dict[str, Any]:
        """验证验证码"""
        key = f"{email}:{purpose}"
        
        if key not in self.verification_codes:
            return {
                'success': False,
                'message': '验证码不存在或已过期',
                'error_code': 'CODE_NOT_FOUND'
            }
        
        stored_data = self.verification_codes[key]
        
        # 检查是否过期
        if datetime.utcnow() > stored_data['expires_at']:
            del self.verification_codes[key]
            return {
                'success': False,
                'message': '验证码已过期',
                'error_code': 'CODE_EXPIRED'
            }
        
        # 检查尝试次数
        if stored_data['attempts'] >= stored_data['max_attempts']:
            del self.verification_codes[key]
            return {
                'success': False,
                'message': '验证码尝试次数过多',
                'error_code': 'TOO_MANY_ATTEMPTS'
            }
        
        # 增加尝试次数
        stored_data['attempts'] += 1
        
        # 验证码码
        if stored_data['code'] == code:
            # 验证成功，删除验证码
            del self.verification_codes[key]
            logger.info(f"邮箱 {email} 的{purpose}验证码验证成功")
            return {
                'success': True,
                'message': '验证码验证成功',
                'data': {
                    'email': email,
                    'purpose': purpose,
                    'verified_at': datetime.utcnow().isoformat()
                }
            }
        else:
            remaining_attempts = stored_data['max_attempts'] - stored_data['attempts']
            return {
                'success': False,
                'message': f'验证码错误，还有{remaining_attempts}次尝试机会',
                'error_code': 'INVALID_CODE',
                'remaining_attempts': remaining_attempts
            }
    
    def generate_reset_token(self, email: str) -> str:
        """生成密码重置令牌"""
        # 生成安全的重置令牌
        token = secrets.token_urlsafe(32)
        
        # 存储重置令牌（1小时有效期）
        self.reset_tokens[token] = {
            'email': email,
            'created_at': datetime.utcnow(),
            'expires_at': datetime.utcnow() + timedelta(hours=1),
            'used': False
        }
        
        logger.info(f"为邮箱 {email} 生成了密码重置令牌")
        return token
    
    def verify_reset_token(self, token: str) -> Dict[str, Any]:
        """验证密码重置令牌"""
        if token not in self.reset_tokens:
            return {
                'success': False,
                'message': '重置令牌无效',
                'error_code': 'INVALID_TOKEN'
            }
        
        token_data = self.reset_tokens[token]
        
        # 检查是否已使用
        if token_data['used']:
            return {
                'success': False,
                'message': '重置令牌已使用',
                'error_code': 'TOKEN_USED'
            }
        
        # 检查是否过期
        if datetime.utcnow() > token_data['expires_at']:
            del self.reset_tokens[token]
            return {
                'success': False,
                'message': '重置令牌已过期',
                'error_code': 'TOKEN_EXPIRED'
            }
        
        return {
            'success': True,
            'message': '重置令牌有效',
            'data': {
                'email': token_data['email'],
                'token': token
            }
        }
    
    def use_reset_token(self, token: str) -> bool:
        """标记重置令牌为已使用"""
        if token in self.reset_tokens:
            self.reset_tokens[token]['used'] = True
            return True
        return False
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({"exp": expire, "type": "access"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def create_refresh_token(self, data: dict) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """验证JWT令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return {
                'success': True,
                'payload': payload
            }
        except jwt.ExpiredSignatureError:
            return {
                'success': False,
                'message': '令牌已过期',
                'error_code': 'TOKEN_EXPIRED'
            }
        except jwt.JWTError:
            return {
                'success': False,
                'message': '令牌无效',
                'error_code': 'INVALID_TOKEN'
            }
    
    def generate_session_id(self) -> str:
        """生成会话ID"""
        return secrets.token_urlsafe(32)
    
    def hash_session_id(self, session_id: str) -> str:
        """哈希会话ID"""
        return hashlib.sha256(session_id.encode()).hexdigest()
    
    def cleanup_expired_codes(self):
        """清理过期的验证码和令牌"""
        current_time = datetime.utcnow()
        
        # 清理过期验证码
        expired_codes = [
            key for key, data in self.verification_codes.items()
            if current_time > data['expires_at']
        ]
        
        for key in expired_codes:
            del self.verification_codes[key]
        
        # 清理过期重置令牌
        expired_tokens = [
            token for token, data in self.reset_tokens.items()
            if current_time > data['expires_at']
        ]
        
        for token in expired_tokens:
            del self.reset_tokens[token]
        
        if expired_codes or expired_tokens:
            logger.info(f"清理了 {len(expired_codes)} 个过期验证码和 {len(expired_tokens)} 个过期重置令牌")
    
    def get_verification_code_info(self, email: str, purpose: str = "login") -> Optional[Dict[str, Any]]:
        """获取验证码信息（用于调试）"""
        key = f"{email}:{purpose}"
        if key in self.verification_codes:
            data = self.verification_codes[key].copy()
            # 不返回实际验证码，只返回状态信息
            data.pop('code', None)
            return data
        return None

# 创建全局认证服务实例
auth_service = AuthService()