# SynTour 阿里云生产级部署实施指南

## 服务器信息

* **服务器**: 阿里云 ECS

* **系统**: Ubuntu 22.04.5 LTS

* **主机名**: iZj6c4zk6mwb25zt3q3e34Z

* **内网IP**: **************

* **用户**: root

* **SSH连接**: `ssh root@[公网IP]`

## 1. 服务器初始化和安全配置

### 1.1 系统更新

```bash
# 更新系统包
apt update && apt upgrade -y

# 安装基础工具
apt install -y curl wget git unzip software-properties-common build-essential

# 设置时区
timedatectl set-timezone Asia/Shanghai

# 查看系统信息
uname -a
df -h
free -h
```

### 1.2 创建应用用户

```bash
# 创建syntour用户
useradd -m -s /bin/bash syntour
usermod -aG sudo syntour

# 设置密码
passwd syntour
# 输入密码: SynTour2024!

# 创建应用目录
mkdir -p /opt/syntour
chown syntour:syntour /opt/syntour

# 创建日志目录
mkdir -p /var/log/syntour
chown syntour:syntour /var/log/syntour
```

### 1.3 SSH安全配置

```bash
# 备份SSH配置
cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

# 编辑SSH配置
nano /etc/ssh/sshd_config

# 修改以下配置项:
# Port 22
# PermitRootLogin yes  # 生产环境建议改为no
# PasswordAuthentication yes
# PubkeyAuthentication yes
# MaxAuthTries 3
# ClientAliveInterval 300
# ClientAliveCountMax 2

# 重启SSH服务
systemctl restart sshd
systemctl status sshd
```

### 1.4 防火墙配置

```bash
# 安装并启用UFW
apt install -y ufw

# 默认策略
ufw default deny incoming
ufw default allow outgoing

# 允许SSH
ufw allow 22/tcp

# 允许HTTP/HTTPS
ufw allow 80/tcp
ufw allow 443/tcp

# 允许应用端口（仅内网）
ufw allow from 172.30.0.0/16 to any port 8000  # 后端API
ufw allow from 172.30.0.0/16 to any port 3000  # 前端
ufw allow from 127.0.0.1 to any port 5432      # PostgreSQL
ufw allow from 127.0.0.1 to any port 6379      # Redis

# 启用防火墙
ufw --force enable
ufw status verbose
```

## 2. 环境依赖安装

### 2.1 安装Node.js 18

```bash
# 添加NodeSource仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# 安装Node.js
apt install -y nodejs

# 验证安装
node --version  # 应该显示v18.x.x
npm --version

# 安装yarn（可选）
npm install -g yarn

# 配置npm镜像（可选，提高下载速度）
npm config set registry https://registry.npmmirror.com
```

### 2.2 安装Python 3.9+

```bash
# Ubuntu 22.04默认Python版本检查
python3 --version  # 应该是3.10+

# 安装Python开发工具
apt install -y python3-pip python3-venv python3-dev

# 安装系统依赖
apt install -y libpq-dev libssl-dev libffi-dev

# 升级pip
pip3 install --upgrade pip

# 验证安装
python3 --version
pip3 --version
```

### 2.3 安装PostgreSQL 14

```bash
# 安装PostgreSQL
apt install -y postgresql postgresql-contrib

# 启动并启用服务
systemctl start postgresql
systemctl enable postgresql
systemctl status postgresql

# 设置postgres用户密码
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'PostgreSQL2024!';"

# 创建应用数据库和用户
sudo -u postgres psql << EOF
CREATE DATABASE syntour_production;
CREATE USER syntour_user WITH PASSWORD 'SynTour2024DB!';
GRANT ALL PRIVILEGES ON DATABASE syntour_production TO syntour_user;
ALTER USER syntour_user CREATEDB;
\q
EOF

# 验证连接
sudo -u postgres psql -c "\l"
```

### 2.4 安装Redis 6

```bash
# 安装Redis
apt install -y redis-server

# 配置Redis
cp /etc/redis/redis.conf /etc/redis/redis.conf.backup

# 编辑Redis配置
nano /etc/redis/redis.conf

# 修改以下配置:
# bind 127.0.0.1
# requirepass SynTourRedis2024!
# maxmemory 512mb
# maxmemory-policy allkeys-lru
# save 900 1
# save 300 10
# save 60 10000

# 重启Redis服务
systemctl restart redis-server
systemctl enable redis-server
systemctl status redis-server

# 测试Redis连接
redis-cli ping
# 应该返回PONG
```

### 2.5 安装Nginx

```bash
# 安装Nginx
apt install -y nginx

# 启动并启用服务
systemctl start nginx
systemctl enable nginx
systemctl status nginx

# 测试Nginx
curl http://localhost
# 应该返回Nginx默认页面

# 创建配置目录
mkdir -p /etc/nginx/sites-available
mkdir -p /etc/nginx/sites-enabled
mkdir -p /etc/nginx/ssl
```

### 2.6 安装Docker（可选）

```bash
# 安装Docker依赖
apt install -y apt-transport-https ca-certificates gnupg lsb-release

# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加Docker仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装Docker
apt update
apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动Docker服务
systemctl start docker
systemctl enable docker

# 将syntour用户添加到docker组
usermod -aG docker syntour

# 验证安装
docker --version
docker compose version
```

## 3. 项目代码部署

### 3.1 克隆项目代码

```bash
# 切换到syntour用户
su - syntour

# 进入应用目录
cd /opt/syntour

# 克隆项目（需要替换为实际的Git仓库地址）
# git clone https://github.com/your-username/syntour.git .

# 如果没有Git仓库，可以通过SCP上传代码
# 在本地执行：
# scp -r /path/to/syntour root@[公网IP]:/opt/syntour/

# 设置目录权限
sudo chown -R syntour:syntour /opt/syntour
```

### 3.2 后端环境配置

```bash
# 进入后端目录
cd /opt/syntour/syntour/syntour/backend

# 创建Python虚拟环境
python3 -m venv venv
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 安装Python依赖
pip install -r requirements.txt

# 如果安装失败，可以尝试单独安装问题包
# pip install fastapi==0.104.1
# pip install uvicorn[standard]==0.24.0
# pip install asyncpg==0.29.0
# pip install redis==5.0.1
```

### 3.3 前端环境配置

```bash
# 进入前端目录
cd /opt/syntour/syntour/syntour/frontend

# 安装依赖
npm install

# 构建生产版本
npm run build

# 验证构建结果
ls -la .next/
```

### 3.4 环境变量配置

```bash
# 创建生产环境配置文件
cd /opt/syntour/syntour/syntour
cp .env.example .env.production

# 编辑生产环境配置
nano .env.production

# 配置内容如下：
cat > .env.production << 'EOF'
# 应用环境
ENVIRONMENT=production
NODE_ENV=production

# 前端配置
NEXT_PUBLIC_API_URL=https://yourdomain.com
NEXT_PUBLIC_WS_URL=wss://yourdomain.com

# Google Cloud配置（使用您现有的配置）
GOOGLE_CLOUD_PROJECT=bright-coyote-463315-q8
GOOGLE_CLOUD_LOCATION=us-west1
VERTEX_AI_ENDPOINT=projects/bright-coyote-463315-q8/locations/us-west1/endpoints/1393226367927058432
GOOGLE_APPLICATION_CREDENTIALS=/opt/syntour/syntour/syntour/backend/config/service-account-key.json
GCS_BUCKET_NAME=syntour_users_file_storage

# API Keys（使用您现有的密钥）
GOOGLE_SPEECH_API_KEY=AIzaSyDt4pPPeBhLIF8_TxNVjoMF63uYM9Hxnds
NEXT_PUBLIC_GOOGLE_MAPS_JAVASCRIPT_API_KEY=AIzaSyBmNIhpkcIfhZ6OCpZoB2AtcgnhCFj1Ngk
GOOGLE_PLACES_API_KEY=AIzaSyBZMEhRm2p68PFVWjMoCeks4kRnjXnTt_o
AMADEUS_API_KEY=********************************
AMADEUS_API_SECRET=oMuBHfRmIkuvjvKR
TOMORROW_IO_API_KEY=********************************
FLIGHT_API_KEY=68aebb62363ff8076b0e9088
GEOAPIFY_API_KEY=********************************
HOTELBEDS_API_KEY=8f6d10b8368ccb62ef25e47b62e743df
HOTELBEDS_SECRET=9b6ee9f605
OPEN_WEATHER_MAP_API_KEY=9b1a2f0239c36bb2c275e030d29ad64b

# 数据库配置
DATABASE_URL=postgresql://syntour_user:SynTour2024DB!@localhost:5432/syntour_production

# Redis配置
REDIS_URL=redis://:SynTourRedis2024!@localhost:6379/0

# 安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com,**************
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# 性能配置
MAX_CONCURRENT_TASKS=20
HEALTH_CHECK_INTERVAL=30.0
HTTP_POOL_MAX_SIZE=50
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60
CACHE_TTL_SECONDS=300

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=50MB
LOG_BACKUP_COUNT=10
EOF

# 设置环境变量文件权限
chmod 600 .env.production
```

### 3.5 Google Cloud服务账户配置

```bash
# 创建配置目录
mkdir -p /opt/syntour/syntour/syntour/backend/config

# 将Google Cloud服务账户密钥文件上传到服务器
# 在本地执行：
# scp /path/to/service-account-key.json root@[公网IP]:/opt/syntour/syntour/syntour/backend/config/

# 设置密钥文件权限
chmod 600 /opt/syntour/syntour/syntour/backend/config/service-account-key.json
chown syntour:syntour /opt/syntour/syntour/syntour/backend/config/service-account-key.json
```

## 4. 数据库和缓存设置

### 4.1 PostgreSQL配置优化

```bash
# 编辑PostgreSQL配置
sudo nano /etc/postgresql/14/main/postgresql.conf

# 优化配置（根据服务器内存调整）
# shared_buffers = 256MB
# effective_cache_size = 1GB
# work_mem = 4MB
# maintenance_work_mem = 64MB
# max_connections = 100
# listen_addresses = 'localhost'

# 编辑访问控制
sudo nano /etc/postgresql/14/main/pg_hba.conf

# 确保包含以下行：
# local   all             all                                     peer
# host    all             all             127.0.0.1/32            md5
# host    all             all             ::1/128                 md5

# 重启PostgreSQL
sudo systemctl restart postgresql
```

### 4.2 创建数据库表

```bash
# 切换到syntour用户
su - syntour
cd /opt/syntour/syntour/syntour/backend
source venv/bin/activate

# 创建基础表结构
psql "postgresql://syntour_user:SynTour2024DB!@localhost:5432/syntour_production" << 'EOF'
-- 系统健康日志表
CREATE TABLE IF NOT EXISTS system_health_logs (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    component VARCHAR(100),
    status VARCHAR(20),
    details JSONB
);

-- 错误日志表
CREATE TABLE IF NOT EXISTS error_logs (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    error_id VARCHAR(100),
    category VARCHAR(50),
    severity VARCHAR(20),
    message TEXT,
    context JSONB
);

-- 用户会话表（可选）
CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP
);

-- 搜索历史表（可选）
CREATE TABLE IF NOT EXISTS search_history (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255),
    query TEXT,
    results JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_system_health_logs_timestamp ON system_health_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_error_logs_timestamp ON error_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_search_history_session_id ON search_history(session_id);

\q
EOF

# 验证表创建
psql "postgresql://syntour_user:SynTour2024DB!@localhost:5432/syntour_production" -c "\dt"
```

### 4.3 Redis配置验证

```bash
# 测试Redis连接
redis-cli -a SynTourRedis2024! ping

# 测试基本操作
redis-cli -a SynTourRedis2024! << 'EOF'
SET test_key "Hello SynTour"
GET test_key
DEL test_key
EXIT
EOF
```

## 5. Nginx配置

### 5.1 创建Nginx配置文件

```bash
# 创建SynTour站点配置
sudo nano /etc/nginx/sites-available/syntour

# 配置内容：
cat > /etc/nginx/sites-available/syntour << 'EOF'
# SynTour Nginx配置
upstream backend {
    server 127.0.0.1:8000;
    keepalive 32;
}

upstream frontend {
    server 127.0.0.1:3000;
    keepalive 32;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS配置
server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL证书配置（稍后配置）
    # ssl_certificate /etc/nginx/ssl/syntour.crt;
    # ssl_certificate_key /etc/nginx/ssl/syntour.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # 日志配置
    access_log /var/log/nginx/syntour_access.log;
    error_log /var/log/nginx/syntour_error.log;

    # 文件上传大小限制
    client_max_body_size 50M;

    # 前端路由
    location / {
        proxy_pass http://frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # API路由
    location /api/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # WebSocket支持
    location /ws {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        
        # 启用gzip压缩
        gzip on;
        gzip_vary on;
        gzip_types text/css application/javascript image/svg+xml;
    }

    # 健康检查
    location /health {
        proxy_pass http://backend/health;
        access_log off;
    }
}
EOF

# 启用站点配置
sudo ln -sf /etc/nginx/sites-available/syntour /etc/nginx/sites-enabled/

# 删除默认配置
sudo rm -f /etc/nginx/sites-enabled/default

# 测试Nginx配置
sudo nginx -t

# 重新加载Nginx
sudo systemctl reload nginx
```

### 5.2 SSL证书配置（Let's Encrypt）

```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-nginx

# 临时配置HTTP版本用于证书验证
sudo nano /etc/nginx/sites-available/syntour-temp

cat > /etc/nginx/sites-available/syntour-temp << 'EOF'
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    location / {
        return 301 https://$server_name$request_uri;
    }
}
EOF

# 启用临时配置
sudo ln -sf /etc/nginx/sites-available/syntour-temp /etc/nginx/sites-enabled/syntour
sudo nginx -t
sudo systemctl reload nginx

# 获取SSL证书（需要替换为实际域名）
# sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet

# 恢复正式配置
sudo ln -sf /etc/nginx/sites-available/syntour /etc/nginx/sites-enabled/syntour
sudo nginx -t
sudo systemctl reload nginx
```

## 6. 系统服务配置

### 6.1 创建后端服务

```bash
# 创建后端systemd服务文件
sudo nano /etc/systemd/system/syntour-backend.service

cat > /etc/systemd/system/syntour-backend.service << 'EOF'
[Unit]
Description=SynTour Backend API
After=network.target postgresql.service redis.service
Requires=postgresql.service redis.service

[Service]
Type=exec
User=syntour
Group=syntour
WorkingDirectory=/opt/syntour/syntour/syntour/backend
Environment=PATH=/opt/syntour/syntour/syntour/backend/venv/bin
EnvironmentFile=/opt/syntour/syntour/syntour/.env.production
ExecStart=/opt/syntour/syntour/syntour/backend/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5

# 安全配置
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/opt/syntour /var/log/syntour

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF
```

### 6.2 创建前端服务

```bash
# 创建前端systemd服务文件
sudo nano /etc/systemd/system/syntour-frontend.service

cat > /etc/systemd/system/syntour-frontend.service << 'EOF'
[Unit]
Description=SynTour Frontend
After=network.target

[Service]
Type=exec
User=syntour
Group=syntour
WorkingDirectory=/opt/syntour/syntour/syntour/frontend
EnvironmentFile=/opt/syntour/syntour/syntour/.env.production
ExecStart=/usr/bin/npm start
Restart=always
RestartSec=5

# 安全配置
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/opt/syntour

[Install]
WantedBy=multi-user.target
EOF
```

### 6.3 启用和启动服务

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable syntour-backend
sudo systemctl enable syntour-frontend

# 启动服务
sudo systemctl start syntour-backend
sudo systemctl start syntour-frontend

# 检查服务状态
sudo systemctl status syntour-backend
sudo systemctl status syntour-frontend

# 查看服务日志
sudo journalctl -u syntour-backend -f
sudo journalctl -u syntour-frontend -f
```

## 7. 监控和日志配置

### 7.1 创建健康检查脚本

```bash
# 创建监控脚本目录
sudo mkdir -p /opt/syntour/scripts

# 创建健康检查脚本
sudo nano /opt/syntour/scripts/health-check.sh

cat > /opt/syntour/scripts/health-check.sh << 'EOF'
#!/bin/bash

# SynTour健康检查脚本
LOG_FILE="/var/log/syntour/health-check.log"
ALERT_EMAIL="<EMAIL>"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# 检查服务状态
check_service() {
    local service=$1
    if ! systemctl is-active --quiet $service; then
        echo "[$DATE] ERROR: Service $service is down" >> $LOG_FILE
        # 尝试重启服务
        systemctl restart $service
        sleep 10
        if systemctl is-active --quiet $service; then
            echo "[$DATE] INFO: Service $service restarted successfully" >> $LOG_FILE
        else
            echo "[$DATE] CRITICAL: Failed to restart $service" >> $LOG_FILE
            # 发送告警邮件（需要配置邮件服务）
            # echo "Service $service is down and failed to restart" | mail -s "SynTour Alert" $ALERT_EMAIL
        fi
        return 1
    fi
    return 0
}

# 检查HTTP响应
check_http() {
    local url=$1
    local expected_code=$2
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" $url)
    
    if [ "$response_code" != "$expected_code" ]; then
        echo "[$DATE] ERROR: $url returned $response_code, expected $expected_code" >> $LOG_FILE
        return 1
    fi
    return 0
}

# 检查磁盘空间
check_disk() {
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $usage -gt 80 ]; then
        echo "[$DATE] WARNING: Disk usage is ${usage}%" >> $LOG_FILE
    fi
    if [ $usage -gt 90 ]; then
        echo "[$DATE] CRITICAL: Disk usage is ${usage}%" >> $LOG_FILE
        # 清理日志文件
        find /var/log -name "*.log" -mtime +7 -delete
        find /opt/syntour -name "*.log" -mtime +7 -delete
    fi
}

# 检查内存使用
check_memory() {
    local mem_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    if [ $mem_usage -gt 80 ]; then
        echo "[$DATE] WARNING: Memory usage is ${mem_usage}%" >> $LOG_FILE
    fi
    if [ $mem_usage -gt 90 ]; then
        echo "[$DATE] CRITICAL: Memory usage is ${mem_usage}%" >> $LOG_FILE
    fi
}

# 执行检查
echo "[$DATE] Starting health check..." >> $LOG_FILE

# 检查系统服务
check_service "syntour-backend"
check_service "syntour-frontend"
check_service "postgresql"
check_service "redis-server"
check_service "nginx"

# 检查HTTP响应
check_http "http://localhost:8000/health" "200"
check_http "http://localhost:3000" "200"
check_http "http://localhost" "301"  # HTTP重定向

# 检查系统资源
check_disk
check_memory

echo "[$DATE] Health check completed" >> $LOG_FILE
EOF

# 设置脚本权限
sudo chmod +x /opt/syntour/scripts/health-check.sh
sudo chown syntour:syntour /opt/syntour/scripts/health-check.sh
```

### 7.2 配置定时任务

```bash
# 为syntour用户配置crontab
sudo -u syntour crontab -e

# 添加以下任务：
# 每5分钟执行健康检查
*/5 * * * * /opt/syntour/scripts/health-check.sh

# 每天凌晨2点清理日志
0 2 * * * find /var/log/syntour -name "*.log" -mtime +30 -delete

# 每周日凌晨3点重启服务（可选）
# 0 3 * * 0 systemctl restart syntour-backend syntour-frontend
```

### 7.3 日志轮转配置

```bash
# 创建日志轮转配置
sudo nano /etc/logrotate.d/syntour

cat > /etc/logrotate.d/syntour << 'EOF'
/var/log/syntour/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 syntour syntour
    postrotate
        systemctl reload syntour-backend syntour-frontend
    endscript
}

/opt/syntour/syntour/syntour/backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 syntour syntour
}
EOF
```

## 8. 部署验证和测试

### 8.1 服务状态检查

```bash
# 检查所有服务状态
sudo systemctl status syntour-backend syntour-frontend postgresql redis-server nginx

# 检查端口监听
sudo netstat -tlnp | grep -E ':80|:443|:3000|:8000|:5432|:6379'

# 检查进程
ps aux | grep -E 'syntour|nginx|postgres|redis'
```

### 8.2 API测试

```bash
# 测试后端健康检查
curl http://localhost:8000/health

# 测试前端
curl http://localhost:3000

# 测试Nginx代理
curl http://localhost/api/health

# 测试数据库连接
psql "postgresql://syntour_user:SynTour2024DB!@localhost:5432/syntour_production" -c "SELECT version();"

# 测试Redis连接
redis-cli -a SynTourRedis2024! ping
```

### 8.3 性能测试

```bash
# 安装性能测试工具
sudo apt install -y apache2-utils

# 简单压力测试
ab -n 100 -c 10 http://localhost:8000/health

# 监控系统资源
top
htop  # 如果已安装
iostat -x 1
```

## 9. 故障排除

### 9.1 常见问题诊断

#### 服务启动失败

```bash
# 查看服务状态和日志
sudo systemctl status syntour-backend
sudo journalctl -u syntour-backend -n 50

# 检查配置文件
sudo nginx -t
python3 -m py_compile /opt/syntour/syntour/syntour/backend/main.py

# 检查端口占用
sudo lsof -i :8000
sudo lsof -i :3000
```

#### 数据库连接问题

```bash
# 检查PostgreSQL状态
sudo systemctl status postgresql

# 测试数据库连接
sudo -u postgres psql -c "\l"
psql "postgresql://syntour_user:SynTour2024DB!@localhost:5432/syntour_production" -c "SELECT 1;"

# 检查数据库日志
sudo tail -f /var/log/postgresql/postgresql-14-main.log
```

#### 内存不足问题

```bash
# 检查内存使用
free -h
ps aux --sort=-%mem | head -10

# 清理系统缓存
sudo sync
sudo echo 3 > /proc/sys/vm/drop_caches

# 重启服务释放内存
sudo systemctl restart syntour-backend syntour-frontend
```

### 9.2 日志分析

```bash
# 查看应用日志
sudo tail -f /var/log/syntour/health-check.log
sudo journalctl -u syntour-backend -f
sudo journalctl -u syntour-frontend -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/syntour_access.log
sudo tail -f /var/log/nginx/syntour_error.log

# 查看系统日志
sudo tail -f /var/log/syslog
sudo dmesg | tail -20
```

### 9.3 性能优化

```bash
# 调整系统参数
echo 'net.core.somaxconn = 65535' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 65535' | sudo tee -a /etc/sysctl.conf
echo 'fs.file-max = 100000' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 调整用户限制
echo 'syntour soft nofile 65535' | sudo tee -a /etc/security/limits.conf
echo 'syntour hard nofile 65535' | sudo tee -a /etc/security/limits.conf
```

## 10. 备份和恢复

### 10.1 数据库备份

```bash
# 创建备份脚本
sudo nano /opt/syntour/scripts/backup-database.sh

cat > /opt/syntour/scripts/backup-database.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/opt/syntour/backups/database"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="syntour_production"
DB_USER="syntour_user"
DB_PASSWORD="SynTour2024DB!"

mkdir -p $BACKUP_DIR

# 创建数据库备份
PGPASSWORD=$DB_PASSWORD pg_dump -h localhost -U $DB_USER -d $DB_NAME | gzip > $BACKUP_DIR/syntour_$DATE.sql.gz

# 保留最近30天的备份
find $BACKUP_DIR -name "syntour_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: syntour_$DATE.sql.gz"
EOF

sudo chmod +x /opt/syntour/scripts/backup-database.sh
sudo chown syntour:syntour /opt/syntour/scripts/backup-database.sh

# 添加到定时任务
sudo -u syntour crontab -e
# 添加：每天凌晨1点备份数据库
# 0 1 * * * /opt/syntour/scripts/backup-database.sh
```

### 10.2 应用备份

```bash
# 创建应用备份脚本
sudo nano /opt/syntour/scripts/backup-application.sh

cat > /opt/syntour/scripts/backup-application.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/opt/syntour/backups/application"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/opt/syntour"

mkdir -p $BACKUP_DIR

# 备份应用代码和配置
tar -czf $BACKUP_DIR/syntour_app_$DATE.tar.gz \
    --exclude='node_modules' \
    --exclude='venv' \
    --exclude='*.log' \
    --exclude='.git' \
    --exclude='backups' \
    $APP_DIR

# 备份配置文件
cp /opt/syntour/syntour/syntour/.env.production $BACKUP_DIR/env_$DATE
cp /etc/nginx/sites-available/syntour $BACKUP_DIR/nginx_$DATE
cp /etc/systemd/system/syntour-*.service $BACKUP_DIR/

# 保留最近7天的备份
find $BACKUP_DIR -name "syntour_app_*.tar.gz" -mtime +7 -delete

echo "Application backup completed: syntour_app_$DATE.tar.gz"
EOF

sudo chmod +x /opt/syntour/scripts/backup-application.sh
sudo chown syntour:syntour /opt/syntour/scripts/backup-application.sh
```

## 11. 安全加固

### 11.1 系统安全

```bash
# 安装fail2ban防止暴力破解
sudo apt install -y fail2ban

# 配置fail2ban
sudo nano /etc/fail2ban/jail.local

cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/syntour_error.log
maxretry = 5

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/syntour_error.log
maxretry = 10
EOF

# 启动fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
sudo systemctl status fail2ban
```

### 11.2 应用安全

```bash
# 设置文件权限
sudo chown -R syntour:syntour /opt/syntour
sudo chmod -R 755 /opt/syntour
sudo chmod 600 /opt/syntour/syntour/syntour/.env.production
sudo chmod 600 /opt/syntour/syntour/syntour/backend/config/service-account-key.json

# 禁用不必要的服务
sudo systemctl disable apache2 2>/dev/null || true
sudo systemctl stop apache2 2>/dev/null || true

# 更新系统
sudo apt update && sudo apt upgrade -y
sudo apt autoremove -y
```

## 12. 最终验证

### 12.1 完整功能测试

```bash
# 创建测试脚本
cat > /tmp/test-syntour.sh << 'EOF'
#!/bin/bash

echo "=== SynTour 部署验证测试 ==="

# 测试后端API
echo "1. 测试后端健康检查..."
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 后端API正常"
else
    echo "❌ 后端API异常"
fi

# 测试前端
echo "2. 测试前端服务..."
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常"
fi

# 测试数据库
echo "3. 测试数据库连接..."
if psql "postgresql://syntour_user:SynTour2024DB!@localhost:5432/syntour_production" -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接异常"
fi

# 测试Redis
echo "4. 测试Redis连接..."
if redis-cli -a SynTourRedis2024! ping > /dev/null 2>&1; then
    echo "✅ Redis连接正常"
else
    echo "❌ Redis连接异常"
fi

# 测试Nginx
echo "5. 测试Nginx代理..."
if curl -f http://localhost/api/health > /dev/null 2>&1; then
    echo "✅ Nginx代理正常"
else
    echo "❌ Nginx代理异常"
fi

# 检查服务状态
echo "6. 检查系统服务状态..."
for service in syntour-backend syntour-frontend postgresql redis-server nginx; do
    if systemctl is-active --quiet $service; then
        echo "✅ $service 运行正常"
    else
        echo "❌ $service 服务异常"
    fi
done

# 检查磁盘空间
echo "7. 检查系统资源..."
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')

echo "磁盘使用率: ${DISK_USAGE}%"
echo "内存使用率: ${MEM_USAGE}%"

if [ $DISK_USAGE -lt 80 ] && [ $MEM_USAGE -lt 80 ]; then
    echo "✅ 系统资源正常"
else
    echo "⚠️ 系统资源使用率较高"
fi

echo "=== 测试完成 ==="
EOF

chmod +x /tmp/test-syntour.sh
/tmp/test-syntour.sh
```

### 12.2 部署完成清单

* [ ] 系统更新和基础工具安装

* [ ] 用户和目录创建

* [ ] 防火墙配置

* [ ] Node.js 18安装

* [ ] Python 3.9+安装

* [ ] PostgreSQL 14安装和配置

* [ ] Redis 6安装和配置

* [ ] Nginx安装和配置

* [ ] 项目代码部署

* [ ] 环境变量配置

* [ ] Google Cloud服务账户配置

* [ ] 数据库表创建

* [ ] 系统服务配置

* [ ] 健康检查脚本

* [ ] 日志轮转配置

* [ ] 备份脚本配置

* [ ] 安全加固

* [ ] 功能测试验证

## 13. 后续维护

### 13.1 日常维护任务

* 每日检查服务状态和日志

* 每周检查系统资源使用情况

* 每月更新系统补丁

* 每季度检查备份完整性

* 定期更新SSL证书

### 13.2 监控指标

* 服务可用性

* 响应时间

* 错误率

* 系统资源使用率

* 数据库性能

* 缓存命中率

### 13.3 告警设置

* 服务宕机告警

* 高资源使用告警

* 磁盘空间不足告警

* 数据库连接异常告警

* SSL证书过期提醒

***

## 总结

本指南提供了SynTour项目在阿里云Ubuntu 22.04.5 LTS服务器上的完整生产级部署方案。按照本指南的步骤执行，您将获得一个安全、稳定、高性能的生产环境。

如果在部署过程中遇到问题，请参考故障排除章节，或查看相关服务的日志文件进行诊断。

**重要提醒**：

1. 请及时更改所有默认密码
2. 配置实际的域名和SSL证书
3. 设置邮件告警服务
4. 定期备份重要数据
5. 监控系统性能和安全状态

