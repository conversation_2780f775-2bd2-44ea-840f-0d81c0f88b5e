// src/ui/PreferenceTags.tsx
"use client";
import { useMemo, useRef, useState } from "react";

export type PreferenceTagsProps = {
  title: string;
  options: string[];
  selected: string[];
  onChange: (next: string[]) => void;
};

function norm(s: string) {
  return s.replace(/\s+/g, " ").trim();
}

export default function PreferenceTags({
  title,
  options,
  selected,
  onChange
}: PreferenceTagsProps) {
  const [items, setItems] = useState<string[]>(options);
  const [adding, setAdding] = useState(false);
  const [draft, setDraft] = useState("");
  const inputRef = useRef<HTMLInputElement | null>(null);

  const isActive = (v: string) => selected.includes(v);

  const allItems = useMemo(() => {
    // 保证已有项在前，自定义在后；不打乱原始顺序
    const base = [...new Set(items.map(norm))];
    return base;
  }, [items]);

  const toggle = (v: string) => {
    const n = norm(v);
    if (!n) return;
    onChange(isActive(n) ? selected.filter(x => x !== n) : [...selected, n]);
  };

  const commitValues = (raw: string) => {
    // 允许以逗号/分号分隔一次输入多个
    const parts = raw
      .split(/[;,]/g)
      .map(norm)
      .filter(Boolean);

    if (parts.length === 0) return;

    // 合并到 items，去重
    setItems(prev => {
      const set = new Set(prev.map(norm));
      parts.forEach(p => set.add(p));
      return Array.from(set);
    });

    // 立即选中这些新值（以及保留已选）
    const cur = new Set(selected);
    parts.forEach(p => cur.add(p));
    onChange(Array.from(cur));
  };

  const onDraftCommit = () => {
    const n = norm(draft);
    if (!n) {
      setAdding(false);
      setDraft("");
      return;
    }
    commitValues(n);
    setAdding(false);
    setDraft("");
  };

  const onKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" || e.key === "Tab") {
      e.preventDefault();
      onDraftCommit();
    } else if (e.key === "," || e.key === ";") {
      // 输入逗号/分号时，实时切分提交
      e.preventDefault();
      const piece = norm(draft);
      if (piece) commitValues(piece);
      setDraft("");
      // 保持添加模式继续输入下一个
      setAdding(true);
      requestAnimationFrame(() => inputRef.current?.focus());
    } else if (e.key === "Escape") {
      setAdding(false);
      setDraft("");
    }
  };

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-myrNavy">{title}</h3>
        {!adding ? (
          <button
            type="button"
            className="text-sm text-myrNavy/70 hover:text-myrNavy underline underline-offset-4"
            onClick={() => {
              setAdding(true);
              setTimeout(() => inputRef.current?.focus(), 0);
            }}
            aria-label="Add custom option"
          >
            + Custom
          </button>
        ) : (
          <span className="text-sm text-myrNavy/60">Press Enter / , to add</span>
        )}
      </div>

      <div className="flex flex-wrap gap-2">
        {allItems.map(opt => {
          const active = isActive(opt);
          return (
            <button
              key={opt}
              type="button"
              onClick={() => toggle(opt)}
              className={[
                "px-3 py-1.5 rounded-full border text-sm transition-all duration-200 transform hover:scale-105 active:scale-95",
                active
                  ? "bg-gradient-to-r from-my-primary via-my-blue-600 to-my-secondary text-white border-transparent shadow-md ring-2 ring-my-gold-300"
                  : "bg-white/90 backdrop-blur border-gray-200 hover:border-my-blue-400 hover:bg-gray-50"
              ].join(" ")}
            >
              {opt}
            </button>
          );
        })}

        {/* 自定义"标签输入"——显示成一个虚线圆角按钮位 */}
        {adding && (
          <div className="px-2 py-1.5 rounded-full border border-dashed border-myrNavy/40 bg-white/60">
            <input
              ref={inputRef}
              value={draft}
              onChange={e => setDraft(e.target.value)}
              onBlur={onDraftCommit}
              onKeyDown={onKeyDown}
              placeholder="Enter custom option…"
              className="bg-transparent outline-none text-sm px-1 w-[180px]"
            />
          </div>
        )}
      </div>
    </div>
  );
}
