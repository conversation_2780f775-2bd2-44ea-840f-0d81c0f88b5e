"use client";
import Link from "next/link";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { loginSuccess } from "@/store/authSlice";
import { Mail, Lock, ArrowRight, ShieldCheck, Rocket, Eye, EyeOff, Send } from "lucide-react";
import Brand from "@/ui/Brand";

export default function LoginPage(){
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [agree, setAgree] = useState(false);
  const [loading, setLoading] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [useEmailVerification, setUseEmailVerification] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch();

  async function sendVerificationCode() {
    if (!email) {
      alert("请输入邮箱地址");
      return;
    }
    
    setSendingCode(true);
    try {
      const res = await fetch("/api/auth/send-verification", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email })
      });
      
      if (res.ok) {
        setCodeSent(true);
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
        alert("验证码已发送到您的邮箱");
      } else {
        const error = await res.json();
        alert(error.detail || "发送验证码失败");
      }
    } catch (error) {
      alert("发送验证码失败，请检查网络连接");
    } finally {
      setSendingCode(false);
    }
  }

  async function onSubmit(e: React.FormEvent){
    e.preventDefault();
    if(!agree) return alert("Please agree to the Terms and Privacy Policy.");
    
    setLoading(true);
    
    try {
      let endpoint = "/api/auth/login";
      let body: any = { email, password };
      
      if (useEmailVerification) {
        if (!verificationCode) {
          alert("请输入验证码");
          setLoading(false);
          return;
        }
        endpoint = "/api/auth/email-login";
        body = { email, verification_code: verificationCode };
      }
      
      const res = await fetch(endpoint, {
        method: "POST",
        headers: { "Content-Type":"application/json" },
        body: JSON.stringify(body)
      });
      
      if(res.ok){
        const data = await res.json();
        dispatch(loginSuccess({ email, token: data.access_token }));
        router.push("/home");
      } else {
        const error = await res.json();
        alert(error.detail || "登录失败");
      }
    } catch (error) {
      alert("登录失败，请检查网络连接");
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen container-page grid md:grid-cols-2 items-center gap-8 py-12">
      {/* Left promo card */}
      <section className="card p-8">
        <h2 className="text-3xl md:text-4xl font-bold leading-tight">
          Unlock More Quota & Full Features! <span aria-hidden>🚀</span>
        </h2>
        <ul className="mt-6 space-y-3 text-gray-700">
          <li className="flex items-start gap-2"><ShieldCheck className="mt-0.5 text-my-success" /> Register now to boost more quota/day.</li>
          <li className="flex items-start gap-2"><ShieldCheck className="mt-0.5 text-my-success" /> Extra perks: permanent chat storage, faster data retrieval.</li>
        </ul>
        <div className="mt-6 rounded-2xl p-5 badge-gold border">
          <p className="text-sm text-gray-700">Designed for travelers in Malaysia — flights, hotels, foods and attractions at your fingertips.</p>
        </div>

        {/* Demo Credentials */}
        <div className="mt-4 rounded-xl p-4 bg-blue-50 border border-blue-200">
          <h4 className="text-sm font-semibold text-blue-800 mb-2">🔑 Demo Credentials</h4>
          <div className="text-xs text-blue-700 space-y-1">
            <div><strong>Email:</strong> <EMAIL></div>
            <div><strong>Password:</strong> demo123</div>
          </div>
        </div>
      </section>

      {/* Right auth form */}
      <section className="card p-8">
        <div className="flex items-center justify-between mb-4 header-border pb-4">
          <Brand />
        </div>
        <div className="flex items-center gap-2 text-2xl font-semibold mb-4">
          <span className="text-my-primary font-extrabold tracking-tight md:tracking-tighter2 text-2xl md:text-3xl">Welcome back</span>
        </div>

        <button type="button" className="btn btn-secondary w-full mb-4" aria-label="Continue with Google">
          Continue with Google
        </button>

        <div className="relative my-4 text-center">
          <span className="px-3 py-1 text-xs bg-white/80 rounded-full border">Or Continue with Email</span>
          <div className="absolute inset-x-0 top-1/2 -z-10 border-t border-dashed"></div>
        </div>

        <div className="mb-4">
          <div className="flex items-center gap-4 text-sm">
            <label className="flex items-center gap-2 cursor-pointer">
              <input 
                type="radio" 
                name="loginMethod" 
                checked={!useEmailVerification} 
                onChange={() => setUseEmailVerification(false)}
                className="h-4 w-4"
              />
              <span>密码登录</span>
            </label>
            <label className="flex items-center gap-2 cursor-pointer">
              <input 
                type="radio" 
                name="loginMethod" 
                checked={useEmailVerification} 
                onChange={() => setUseEmailVerification(true)}
                className="h-4 w-4"
              />
              <span>邮箱验证码登录</span>
            </label>
          </div>
        </div>

        <form onSubmit={onSubmit} className="space-y-3">
          <label className="block text-sm font-medium mb-1">Email</label>
          <div className="flex items-center gap-2">
            <Mail className="text-gray-400" />
            <input className="input" type="email" placeholder="<EMAIL>" value={email} onChange={e=>setEmail(e.target.value)} required />
          </div>

          {useEmailVerification ? (
            <>
              <label className="block text-sm font-medium mb-1">验证码</label>
              <div className="flex items-center gap-2">
                <Lock className="text-gray-400" />
                <input 
                  className="input flex-1" 
                  type="text" 
                  placeholder="请输入6位验证码" 
                  value={verificationCode} 
                  onChange={e=>setVerificationCode(e.target.value)} 
                  maxLength={6}
                  required 
                />
                <button
                  type="button"
                  onClick={sendVerificationCode}
                  disabled={sendingCode || countdown > 0}
                  className="btn btn-secondary px-3 py-2 text-sm whitespace-nowrap"
                >
                  {sendingCode ? (
                    "发送中..."
                  ) : countdown > 0 ? (
                    `${countdown}s`
                  ) : (
                    <><Send size={16} className="mr-1" />发送验证码</>
                  )}
                </button>
              </div>
            </>
          ) : (
            <>
              <label className="block text-sm font-medium mb-1">Password</label>
              <div className="flex items-center gap-2 relative">
                <Lock className="text-gray-400" />
                <input 
                  className="input pr-10" 
                  type={showPassword ? "text" : "password"} 
                  placeholder="••••••••" 
                  value={password} 
                  onChange={e=>setPassword(e.target.value)} 
                  required 
                />
                <button
                  type="button"
                  className="absolute right-3 text-gray-400 hover:text-gray-600 transition-colors"
                  onClick={() => setShowPassword(!showPassword)}
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
            </>
          )}

          <div className="flex items-center gap-2 mt-1 text-sm">
            <input id="agree" type="checkbox" checked={agree} onChange={()=>setAgree(!agree)} className="h-4 w-4 rounded border-gray-300"/>
            <label htmlFor="agree">I agree to the <a className="link" href="#">Terms</a> and <a className="link" href="#">Privacy Policy</a></label>
          </div>

          <button className="btn btn-primary w-full mt-4" disabled={loading} aria-label="Sign in">
            {loading ? "Signing in..." : <>Sign up / Sign in <ArrowRight className="ml-2" /></>}
          </button>
        </form>

        <div className="mt-4 flex justify-between text-sm">
          <Link className="link" href="/auth/register">Create account</Link>
          <Link className="link" href="/auth/forgot">Forgot password?</Link>
        </div>
      </section>
    </div>
  );
}
