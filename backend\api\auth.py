from fastapi import APIRouter, HTTPException, Depends, status, BackgroundTasks
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr, validator
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import logging

# 导入服务
from services.email_service import email_service
from services.auth_service import auth_service
from database import get_db
from models.user import User

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/auth", tags=["认证"])
security = HTTPBearer()

# Pydantic模型
class EmailVerificationRequest(BaseModel):
    email: EmailStr
    purpose: str = "login"  # login, register, reset_password
    
    @validator('purpose')
    def validate_purpose(cls, v):
        allowed_purposes = ['login', 'register', 'reset_password']
        if v not in allowed_purposes:
            raise ValueError(f'purpose must be one of {allowed_purposes}')
        return v

class VerifyCodeRequest(BaseModel):
    email: EmailStr
    code: str
    purpose: str = "login"
    
    @validator('code')
    def validate_code(cls, v):
        if not v.isdigit() or len(v) != 6:
            raise ValueError('验证码必须是6位数字')
        return v

class EmailLoginRequest(BaseModel):
    email: EmailStr
    code: str
    remember_me: bool = False

class RegisterRequest(BaseModel):
    email: EmailStr
    code: str
    username: str
    password: str
    confirm_password: str
    
    @validator('username')
    def validate_username(cls, v):
        if len(v) < 2 or len(v) > 50:
            raise ValueError('用户名长度必须在2-50个字符之间')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('密码长度至少6个字符')
        return v
    
    @validator('confirm_password')
    def validate_confirm_password(cls, v, values):
        if 'password' in values and v != values['password']:
            raise ValueError('两次输入的密码不一致')
        return v

class PasswordResetRequest(BaseModel):
    email: EmailStr

class PasswordResetConfirmRequest(BaseModel):
    token: str
    new_password: str
    confirm_password: str
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 6:
            raise ValueError('密码长度至少6个字符')
        return v
    
    @validator('confirm_password')
    def validate_confirm_password(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('两次输入的密码不一致')
        return v

class AuthResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    token_type: str = "bearer"

# 依赖函数
def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    """获取当前用户"""
    token = credentials.credentials
    token_data = auth_service.verify_token(token)
    
    if not token_data['success']:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=token_data['message'],
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user_id = token_data['payload'].get('sub')
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user

# API端点
@router.post("/send-verification-code", response_model=AuthResponse)
async def send_verification_code(
    request: EmailVerificationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """发送邮箱验证码"""
    try:
        # 验证邮箱格式
        if not auth_service.validate_email_format(request.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱格式不正确"
            )
        
        # 检查用户是否存在（根据不同目的进行不同处理）
        existing_user = db.query(User).filter(User.email == request.email).first()
        
        if request.purpose == "register" and existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该邮箱已被注册"
            )
        
        if request.purpose in ["login", "reset_password"] and not existing_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="该邮箱尚未注册"
            )
        
        # 生成验证码
        code = auth_service.generate_verification_code(request.email, request.purpose)
        
        # 异步发送邮件
        background_tasks.add_task(
            email_service.send_verification_email,
            request.email,
            code,
            request.purpose
        )
        
        logger.info(f"验证码已发送到邮箱: {request.email}, 目的: {request.purpose}")
        
        return AuthResponse(
            success=True,
            message="验证码已发送，请查收邮件",
            data={
                "email": request.email,
                "purpose": request.purpose,
                "expires_in": 300  # 5分钟
            }
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送验证码失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="发送验证码失败，请稍后重试"
        )

@router.post("/verify-code", response_model=AuthResponse)
async def verify_code(request: VerifyCodeRequest):
    """验证邮箱验证码"""
    try:
        result = auth_service.verify_verification_code(
            request.email,
            request.code,
            request.purpose
        )
        
        if result['success']:
            return AuthResponse(
                success=True,
                message=result['message'],
                data=result['data']
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['message']
            )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证码验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证失败，请稍后重试"
        )

@router.post("/login-with-email", response_model=AuthResponse)
async def login_with_email(
    request: EmailLoginRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """邮箱验证码登录"""
    try:
        # 验证验证码
        verify_result = auth_service.verify_verification_code(
            request.email,
            request.code,
            "login"
        )
        
        if not verify_result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=verify_result['message']
            )
        
        # 查找用户
        user = db.query(User).filter(User.email == request.email).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 检查用户状态
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="账户已被禁用"
            )
        
        # 更新用户登录信息
        user.last_login = datetime.utcnow()
        user.email_verified = True
        db.commit()
        
        # 生成令牌
        access_token_expires = timedelta(minutes=auth_service.access_token_expire_minutes)
        if request.remember_me:
            access_token_expires = timedelta(days=7)  # 记住我：7天
        
        access_token = auth_service.create_access_token(
            data={"sub": str(user.id), "email": user.email},
            expires_delta=access_token_expires
        )
        
        refresh_token = auth_service.create_refresh_token(
            data={"sub": str(user.id), "email": user.email}
        )
        
        logger.info(f"用户 {user.email} 邮箱验证登录成功")
        
        return AuthResponse(
            success=True,
            message="登录成功",
            data={
                "user": {
                    "id": user.id,
                    "email": user.email,
                    "username": user.username,
                    "email_verified": user.email_verified,
                    "last_login": user.last_login.isoformat() if user.last_login else None
                }
            },
            access_token=access_token,
            refresh_token=refresh_token
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"邮箱登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )

@router.post("/register", response_model=AuthResponse)
async def register(
    request: RegisterRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """用户注册"""
    try:
        # 验证验证码
        verify_result = auth_service.verify_verification_code(
            request.email,
            request.code,
            "register"
        )
        
        if not verify_result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=verify_result['message']
            )
        
        # 检查邮箱是否已注册
        if db.query(User).filter(User.email == request.email).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该邮箱已被注册"
            )
        
        # 检查用户名是否已存在
        if db.query(User).filter(User.username == request.username).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # 创建新用户
        hashed_password = auth_service.hash_password(request.password)
        new_user = User(
            email=request.email,
            username=request.username,
            hashed_password=hashed_password,
            email_verified=True,  # 通过邮箱验证注册，直接标记为已验证
            is_active=True,
            created_at=datetime.utcnow()
        )
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        # 发送欢迎邮件
        background_tasks.add_task(
            email_service.send_welcome_email,
            request.email,
            request.username
        )
        
        # 生成令牌
        access_token = auth_service.create_access_token(
            data={"sub": str(new_user.id), "email": new_user.email}
        )
        
        refresh_token = auth_service.create_refresh_token(
            data={"sub": str(new_user.id), "email": new_user.email}
        )
        
        logger.info(f"新用户注册成功: {new_user.email}")
        
        return AuthResponse(
            success=True,
            message="注册成功",
            data={
                "user": {
                    "id": new_user.id,
                    "email": new_user.email,
                    "username": new_user.username,
                    "email_verified": new_user.email_verified,
                    "created_at": new_user.created_at.isoformat()
                }
            },
            access_token=access_token,
            refresh_token=refresh_token
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户注册失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败，请稍后重试"
        )

@router.post("/request-password-reset", response_model=AuthResponse)
async def request_password_reset(
    request: PasswordResetRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """请求密码重置"""
    try:
        # 检查用户是否存在
        user = db.query(User).filter(User.email == request.email).first()
        if not user:
            # 为了安全，不透露用户是否存在
            return AuthResponse(
                success=True,
                message="如果该邮箱已注册，您将收到密码重置邮件"
            )
        
        # 生成重置令牌
        reset_token = auth_service.generate_reset_token(request.email)
        
        # 发送重置邮件
        background_tasks.add_task(
            email_service.send_password_reset_email,
            request.email,
            reset_token
        )
        
        logger.info(f"密码重置邮件已发送到: {request.email}")
        
        return AuthResponse(
            success=True,
            message="密码重置邮件已发送，请查收"
        )
    
    except Exception as e:
        logger.error(f"密码重置请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="请求失败，请稍后重试"
        )

@router.post("/reset-password", response_model=AuthResponse)
async def reset_password(
    request: PasswordResetConfirmRequest,
    db: Session = Depends(get_db)
):
    """重置密码"""
    try:
        # 验证重置令牌
        token_result = auth_service.verify_reset_token(request.token)
        
        if not token_result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=token_result['message']
            )
        
        email = token_result['data']['email']
        
        # 查找用户
        user = db.query(User).filter(User.email == email).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 更新密码
        user.hashed_password = auth_service.hash_password(request.new_password)
        user.updated_at = datetime.utcnow()
        
        # 标记令牌为已使用
        auth_service.use_reset_token(request.token)
        
        db.commit()
        
        logger.info(f"用户 {email} 密码重置成功")
        
        return AuthResponse(
            success=True,
            message="密码重置成功，请使用新密码登录"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"密码重置失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码重置失败，请稍后重试"
        )

@router.get("/me", response_model=AuthResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    return AuthResponse(
        success=True,
        message="获取用户信息成功",
        data={
            "user": {
                "id": current_user.id,
                "email": current_user.email,
                "username": current_user.username,
                "email_verified": current_user.email_verified,
                "is_active": current_user.is_active,
                "created_at": current_user.created_at.isoformat() if current_user.created_at else None,
                "last_login": current_user.last_login.isoformat() if current_user.last_login else None
            }
        }
    )

@router.post("/refresh-token", response_model=AuthResponse)
async def refresh_access_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """刷新访问令牌"""
    try:
        refresh_token = credentials.credentials
        token_data = auth_service.verify_token(refresh_token)
        
        if not token_data['success']:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=token_data['message']
            )
        
        payload = token_data['payload']
        if payload.get('type') != 'refresh':
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
        
        user_id = payload.get('sub')
        user = db.query(User).filter(User.id == user_id).first()
        
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用"
            )
        
        # 生成新的访问令牌
        new_access_token = auth_service.create_access_token(
            data={"sub": str(user.id), "email": user.email}
        )
        
        return AuthResponse(
            success=True,
            message="令牌刷新成功",
            access_token=new_access_token
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"令牌刷新失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="令牌刷新失败"
        )

@router.post("/logout", response_model=AuthResponse)
async def logout(current_user: User = Depends(get_current_user)):
    """用户登出"""
    # 在实际应用中，这里应该将令牌加入黑名单
    # 目前只是返回成功响应
    logger.info(f"用户 {current_user.email} 已登出")
    
    return AuthResponse(
        success=True,
        message="登出成功"
    )