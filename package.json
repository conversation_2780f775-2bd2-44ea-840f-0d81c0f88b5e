{"name": "my-travel-my", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3007", "build": "next build", "start": "next start -p 3007", "lint": "eslint .", "typecheck": "tsc --noEmit", "convert": "node convert.js"}, "dependencies": {"@reduxjs/toolkit": "^2.2.7", "autoprefixer": "^10.4.20", "clsx": "^2.1.1", "lucide-react": "^0.452.0", "next": "14.2.5", "postcss": "^8.4.41", "react": "18.3.1", "react-dom": "18.3.1", "react-redux": "^9.1.2", "sharp": "^0.34.3", "tailwindcss": "^3.4.10", "typescript": "^5.6.2", "zustand": "^5.0.8"}, "devDependencies": {"@types/node": "^20.14.12", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "eslint": "8.57.0", "eslint-config-next": "14.2.5"}, "overrides": {"eslint": "8.57.0", "eslint-config-next": "14.2.5"}}