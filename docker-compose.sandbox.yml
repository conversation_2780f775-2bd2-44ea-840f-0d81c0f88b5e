version: '3.8'

services:
  # 前端服务 - Next.js
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "3007:3007"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_WS_URL=ws://localhost:8000
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - syntour-network

  # 后端服务 - FastAPI
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT}
      - GOOGLE_CLOUD_LOCATION=${GOOGLE_CLOUD_LOCATION}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/backend/config/service-account-key.json
    volumes:
      - ./backend:/app/backend
      - ./backend/config:/app/backend/config
      - ./uploads:/app/uploads
    networks:
      - syntour-network

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - syntour-network

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=syntour_dev
      - POSTGRES_USER=syntour
      - POSTGRES_PASSWORD=syntour_dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database-init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - syntour-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/sandbox.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - syntour-network

volumes:
  postgres_data:
  redis_data:

networks:
  syntour-network:
    driver: bridge