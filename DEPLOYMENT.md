# SynTour 生产环境部署指南

## 概述

本指南提供了 SynTour 项目在阿里云服务器上的完整部署流程。项目采用前后端分离架构，后端使用 FastAPI + Python，前端使用 Next.js + React。

## 服务器信息

- **服务器地址**: `iZj6c4zk6mwb25zt3q3e34Z`
- **操作系统**: Ubuntu 22.04.5 LTS
- **用户**: root
- **密码**: Lw20060607.@@

## 部署文件说明

### 配置文件
- `.env.production` - 生产环境配置文件
- `database-init.sql` - 数据库初始化脚本
- `google-cloud-setup.md` - Google Cloud 配置指南

### 部署脚本
- `server-setup.sh` - 服务器环境初始化脚本
- `deploy.sh` - 项目部署脚本
- `quick-deploy.sh` - 一键部署脚本
- `monitor.sh` - 系统监控脚本

## 快速部署

### 方法一：一键部署（推荐）

```bash
# 给脚本执行权限
chmod +x quick-deploy.sh

# 执行一键部署
./quick-deploy.sh
```

### 方法二：分步部署

#### 1. 服务器环境初始化

```bash
# 上传并执行服务器初始化脚本
scp server-setup.sh root@iZj6c4zk6mwb25zt3q3e34Z:/tmp/
ssh root@iZj6c4zk6mwb25zt3q3e34Z
chmod +x /tmp/server-setup.sh
/tmp/server-setup.sh
```

#### 2. 数据库初始化

```bash
# 上传并执行数据库初始化脚本
scp database-init.sql root@iZj6c4zk6mwb25zt3q3e34Z:/tmp/
ssh root@iZj6c4zk6mwb25zt3q3e34Z
sudo -u postgres psql -f /tmp/database-init.sql
```

#### 3. 配置 Google Cloud 服务账户

请参考 `google-cloud-setup.md` 文件中的详细说明。

#### 4. 部署项目

```bash
# 给脚本执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh iZj6c4zk6mwb25zt3q3e34Z
```

## 部署后验证

### 1. 服务状态检查

```bash
# 使用监控脚本检查
chmod +x monitor.sh
./monitor.sh --all

# 或者手动检查
ssh root@iZj6c4zk6mwb25zt3q3e34Z
systemctl status syntour-backend syntour-frontend nginx postgresql redis-server
```

### 2. API 测试

```bash
# 健康检查
curl http://iZj6c4zk6mwb25zt3q3e34Z:8000/health

# API 文档
curl http://iZj6c4zk6mwb25zt3q3e34Z:8000/docs
```

### 3. 前端访问

在浏览器中访问：
- 前端应用: `http://iZj6c4zk6mwb25zt3q3e34Z`
- API 文档: `http://iZj6c4zk6mwb25zt3q3e34Z:8000/docs`

## 系统架构

### 服务组件

1. **前端服务** (端口 3000)
   - Next.js 应用
   - 通过 Nginx 反向代理

2. **后端服务** (端口 8000)
   - FastAPI 应用
   - 4个 worker 进程
   - 通过 Nginx 反向代理

3. **数据库服务**
   - PostgreSQL 15
   - 数据库名: `syntour_production`
   - 用户: `syntour_user`

4. **缓存服务**
   - Redis 服务器
   - 密码保护
   - 内存限制: 256MB

5. **Web 服务器**
   - Nginx
   - 反向代理配置
   - 静态文件服务

### 目录结构

```
/opt/syntour/
├── syntour/
│   ├── backend/
│   │   ├── venv/              # Python 虚拟环境
│   │   ├── config/            # 配置文件
│   │   │   └── service-account-key.json
│   │   ├── requirements.txt
│   │   └── main.py
│   ├── frontend/
│   │   ├── node_modules/
│   │   ├── .next/             # 构建输出
│   │   ├── package.json
│   │   └── next.config.js
│   └── .env.production        # 环境配置
└── logs/                      # 应用日志
```

## 环境配置

### 主要环境变量

```bash
# 应用环境
ENVIRONment=production
NODE_ENV=production

# 数据库
DATABASE_URL=postgresql://syntour_user:SynTour2024DB!@localhost:5432/syntour_production

# Redis
REDIS_URL=redis://:SynTourRedis2024!@localhost:6379/0

# Google Cloud
GOOGLE_CLOUD_PROJECT=bright-coyote-463315-q8
GOOGLE_APPLICATION_CREDENTIALS=/opt/syntour/syntour/backend/config/service-account-key.json
```

## 系统服务

### Systemd 服务

1. **syntour-backend.service**
   - 后端 API 服务
   - 自动重启
   - 依赖 PostgreSQL 和 Redis

2. **syntour-frontend.service**
   - 前端应用服务
   - 自动重启

### 服务管理命令

```bash
# 启动服务
sudo systemctl start syntour-backend syntour-frontend

# 停止服务
sudo systemctl stop syntour-backend syntour-frontend

# 重启服务
sudo systemctl restart syntour-backend syntour-frontend

# 查看状态
sudo systemctl status syntour-backend syntour-frontend

# 查看日志
sudo journalctl -u syntour-backend -f
sudo journalctl -u syntour-frontend -f
```

## 监控和维护

### 日志管理

- **应用日志**: `/var/log/syntour/`
- **系统日志**: `journalctl -u syntour-*`
- **Nginx 日志**: `/var/log/nginx/`
- **PostgreSQL 日志**: `/var/log/postgresql/`

### 定期维护任务

1. **清理过期会话**
   ```sql
   SELECT cleanup_expired_sessions();
   ```

2. **清理旧日志**
   ```sql
   SELECT cleanup_old_logs(30); -- 保留30天
   ```

3. **数据库备份**
   ```bash
   pg_dump -U syntour_user syntour_production > backup_$(date +%Y%m%d).sql
   ```

### 性能监控

使用监控脚本定期检查系统状态：

```bash
# 完整系统检查
./monitor.sh --all

# 生成监控报告
./monitor.sh --report

# 性能测试
./monitor.sh --performance
```

## 安全配置

### 防火墙设置

```bash
# 查看防火墙状态
sudo ufw status

# 允许的端口
# - 22 (SSH)
# - 80 (HTTP)
# - 443 (HTTPS)
# - 3000 (前端开发)
# - 8000 (后端 API)
```

### 文件权限

- 配置文件: `600` (仅所有者读写)
- 应用目录: `755` (所有者全权限，其他只读执行)
- 日志目录: `755`

### 密码安全

⚠️ **重要**: 部署完成后请立即更改以下默认密码：

1. **数据库密码**: `SynTour2024DB!`
2. **Redis 密码**: `SynTourRedis2024!`
3. **应用管理员密码**: `admin123`
4. **测试用户密码**: `test123`

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 查看详细错误信息
   sudo journalctl -u syntour-backend -n 50
   sudo journalctl -u syntour-frontend -n 50
   ```

2. **数据库连接失败**
   ```bash
   # 检查 PostgreSQL 状态
   sudo systemctl status postgresql
   
   # 测试数据库连接
   sudo -u postgres psql -d syntour_production -c "SELECT 1;"
   ```

3. **Redis 连接失败**
   ```bash
   # 检查 Redis 状态
   sudo systemctl status redis-server
   
   # 测试 Redis 连接
   redis-cli -a SynTourRedis2024! ping
   ```

4. **Google Cloud 认证失败**
   - 检查服务账户密钥文件路径
   - 验证文件权限和内容
   - 参考 `google-cloud-setup.md`

### 紧急恢复

如果系统出现严重问题，可以使用以下步骤恢复：

1. **重新部署**
   ```bash
   ./quick-deploy.sh
   ```

2. **恢复数据库**
   ```bash
   sudo -u postgres psql -d syntour_production < backup_file.sql
   ```

3. **重启所有服务**
   ```bash
   sudo systemctl restart postgresql redis-server nginx syntour-backend syntour-frontend
   ```

## 扩展和优化

### 性能优化

1. **数据库优化**
   - 定期 VACUUM 和 ANALYZE
   - 监控慢查询
   - 调整连接池大小

2. **缓存优化**
   - 调整 Redis 内存限制
   - 优化缓存策略
   - 监控缓存命中率

3. **应用优化**
   - 调整 worker 进程数
   - 优化静态文件缓存
   - 启用 gzip 压缩

### SSL/HTTPS 配置

如需启用 HTTPS，请：

1. 获取 SSL 证书（Let's Encrypt 推荐）
2. 更新 Nginx 配置
3. 重定向 HTTP 到 HTTPS

### 域名配置

1. 配置 DNS 记录指向服务器 IP
2. 更新 Nginx 配置中的 `server_name`
3. 更新环境变量中的域名

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看相关日志文件
2. 运行监控脚本诊断问题
3. 检查网络连接和防火墙设置
4. 验证配置文件和环境变量

---

**部署完成后，请访问 `http://iZj6c4zk6mwb25zt3q3e34Z` 查看应用是否正常运行！**