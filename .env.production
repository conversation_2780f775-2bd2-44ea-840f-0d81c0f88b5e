# Database Configuration
POSTGRES_PASSWORD=SynTour2024!@#
DATABASE_URL=postgresql://syntour_user:SynTour2024!@#@postgres:5432/syntour_db

# Redis Configuration
REDIS_PASSWORD=Redis2024!@#
REDIS_URL=redis://:Redis2024!@#@redis:6379/0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Application Configuration
ENVIRONMENT=production
DEBUG=false
ALLOWED_HOSTS=************,localhost,127.0.0.1
CORS_ORIGINS=http://************,https://************

# API Configuration
API_V1_PREFIX=/api/v1
API_HOST=0.0.0.0
API_PORT=8000

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://************:8000
NEXT_PUBLIC_APP_URL=http://************:3000

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
SECURE_SSL_REDIRECT=false
SESSION_COOKIE_SECURE=false
CSRF_COOKIE_SECURE=false

# Logging
LOG_LEVEL=INFO
LOG_FILE=/app/logs/syntour.log

# Email Configuration (if needed)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH=/app/uploads

# Cache Configuration
CACHE_TTL=3600  # 1 hour
CACHE_MAX_ENTRIES=1000