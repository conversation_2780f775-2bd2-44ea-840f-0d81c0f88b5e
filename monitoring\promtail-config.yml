server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # 系统日志
  - job_name: system
    static_configs:
      - targets:
          - localhost
        labels:
          job: varlogs
          __path__: /var/log/*log

  # Docker容器日志
  - job_name: containers
    static_configs:
      - targets:
          - localhost
        labels:
          job: containerlogs
          __path__: /var/lib/docker/containers/*/*log
    pipeline_stages:
      - json:
          expressions:
            output: log
            stream: stream
            attrs:
      - json:
          expressions:
            tag:
          source: attrs
      - regex:
          expression: (?P<container_name>(?:[^|]*))
          source: tag
      - timestamp:
          format: RFC3339Nano
          source: time
      - labels:
          stream:
          container_name:
      - output:
          source: output

  # SynTour Backend日志
  - job_name: syntour-backend
    static_configs:
      - targets:
          - localhost
        labels:
          job: syntour-backend
          __path__: /app/logs/backend.log
    pipeline_stages:
      - regex:
          expression: '(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \[(?P<level>\w+)\] (?P<message>.*)'
      - timestamp:
          format: '2006-01-02 15:04:05'
          source: timestamp
      - labels:
          level:

  # SynTour Frontend日志
  - job_name: syntour-frontend
    static_configs:
      - targets:
          - localhost
        labels:
          job: syntour-frontend
          __path__: /app/logs/frontend.log
    pipeline_stages:
      - json:
          expressions:
            level: level
            message: msg
            timestamp: time
      - timestamp:
          format: RFC3339
          source: timestamp
      - labels:
          level:

  # Nginx访问日志
  - job_name: nginx-access
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx-access
          __path__: /var/log/nginx/access.log
    pipeline_stages:
      - regex:
          expression: '(?P<remote_addr>[\w\.]+) - (?P<remote_user>[\w]+) \[(?P<time_local>[\w:/]+\s[+\-]\d{4})\] "(?P<method>\w+) (?P<request>[^"]*) (?P<protocol>[^"]*?)" (?P<status>\d+) (?P<body_bytes_sent>\d+) "(?P<http_referer>[^"]*)" "(?P<http_user_agent>[^"]*)"'
      - timestamp:
          format: '02/Jan/2006:15:04:05 -0700'
          source: time_local
      - labels:
          method:
          status:
          remote_addr:

  # Nginx错误日志
  - job_name: nginx-error
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx-error
          __path__: /var/log/nginx/error.log
    pipeline_stages:
      - regex:
          expression: '(?P<timestamp>\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}) \[(?P<level>\w+)\] (?P<message>.*)'
      - timestamp:
          format: '2006/01/02 15:04:05'
          source: timestamp
      - labels:
          level:

  # PostgreSQL日志
  - job_name: postgresql
    static_configs:
      - targets:
          - localhost
        labels:
          job: postgresql
          __path__: /var/log/postgresql/postgresql-*.log
    pipeline_stages:
      - regex:
          expression: '(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3} \w+) \[(?P<pid>\d+)\] (?P<level>\w+): (?P<message>.*)'
      - timestamp:
          format: '2006-01-02 15:04:05.000 MST'
          source: timestamp
      - labels:
          level:
          pid:

  # Redis日志
  - job_name: redis
    static_configs:
      - targets:
          - localhost
        labels:
          job: redis
          __path__: /var/log/redis/redis-server.log
    pipeline_stages:
      - regex:
          expression: '(?P<pid>\d+):(?P<role>\w) (?P<timestamp>\d{2} \w{3} \d{4} \d{2}:\d{2}:\d{2}\.\d{3}) (?P<level>[\*\#\-\.]) (?P<message>.*)'
      - timestamp:
          format: '02 Jan 2006 15:04:05.000'
          source: timestamp
      - labels:
          level:
          role:
          pid:

  # 应用程序错误日志
  - job_name: app-errors
    static_configs:
      - targets:
          - localhost
        labels:
          job: app-errors
          __path__: /app/logs/error.log
    pipeline_stages:
      - json:
          expressions:
            level: level
            message: message
            timestamp: timestamp
            service: service
            error: error
      - timestamp:
          format: RFC3339
          source: timestamp
      - labels:
          level:
          service:

  # 审计日志
  - job_name: audit
    static_configs:
      - targets:
          - localhost
        labels:
          job: audit
          __path__: /app/logs/audit.log
    pipeline_stages:
      - json:
          expressions:
            timestamp: timestamp
            user_id: user_id
            action: action
            resource: resource
            ip_address: ip_address
      - timestamp:
          format: RFC3339
          source: timestamp
      - labels:
          user_id:
          action:
          resource: