# SynTour 生产级部署准备文档

## 1. 项目架构概述

### 1.1 技术栈

**前端 (Frontend)**
- **框架**: Next.js 14.2.5 (React 18.3.1)
- **样式**: Tailwind CSS 3.4.10
- **状态管理**: Redux Toolkit 2.2.7, Zustand 5.0.8
- **地图服务**: Google Maps API (@react-google-maps/api 2.20.7)
- **构建工具**: TypeScript 5.6.2, ESLint

**后端 (Backend)**
- **框架**: FastAPI 0.104.1
- **运行时**: Python 3.8+ with Uvicorn
- **异步处理**: asyncio, aiohttp 3.9.1
- **数据库**: PostgreSQL (asyncpg 0.29.0)
- **缓存**: Redis 5.0.1
- **文件处理**: Google Cloud Storage
- **AI服务**: Google Cloud Vertex AI, Gemini

**第三方服务集成**
- Google Cloud Platform (AI, Storage, Speech-to-Text)
- Amadeus API (航班、酒店)
- Tomorrow.io (天气服务)
- OpenWeatherMap
- Geoapify (地理服务)
- HotelBeds API
- FlightAPI.io

### 1.2 系统架构

```mermaid
graph TD
    A[用户浏览器] --> B[Next.js 前端]
    B --> C[FastAPI 后端]
    C --> D[PostgreSQL 数据库]
    C --> E[Redis 缓存]
    C --> F[Google Cloud AI]
    C --> G[第三方API服务]
    C --> H[Google Cloud Storage]
    
    subgraph "监控与日志"
        I[健康检查]
        J[性能监控]
        K[错误追踪]
        L[日志系统]
    end
    
    C --> I
    C --> J
    C --> K
    C --> L
```

## 2. 环境配置要求

### 2.1 系统要求

**最低配置**
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 50GB SSD
- 网络: 100Mbps

**推荐配置**
- CPU: 8核心
- 内存: 16GB RAM
- 存储: 100GB SSD
- 网络: 1Gbps

### 2.2 软件依赖

**系统级依赖**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y python3.8 python3-pip nodejs npm postgresql redis-server nginx

# CentOS/RHEL
sudo yum install -y python38 python3-pip nodejs npm postgresql redis nginx
```

**Python 依赖**
```bash
# 核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-dotenv==1.0.0
python-multipart==0.0.6
pydantic==2.5.0

# Google Cloud
google-cloud-aiplatform==1.70.0
google-cloud-speech==2.21.0
google-cloud-storage==2.10.0
google-generativeai==0.3.2

# 数据库和缓存
asyncpg==0.29.0
redis==5.0.1

# 性能和监控
circuitbreaker==1.4.0
asyncio-throttle==1.0.2
tenacity==8.2.3
psutil==5.9.6
```

**Node.js 依赖**
```json
{
  "dependencies": {
    "next": "^14.2.5",
    "react": "18.3.1",
    "react-dom": "18.3.1",
    "@react-google-maps/api": "^2.20.7",
    "@reduxjs/toolkit": "^2.2.7",
    "tailwindcss": "^3.4.10",
    "typescript": "^5.6.2"
  }
}
```

### 2.3 环境变量配置

**生产环境配置文件 (.env.production)**
```bash
# 应用环境
NODE_ENV=production
ENVIRONMENT=production

# 前端配置
NEXT_PUBLIC_API_URL=https://api.syntour.com
NEXT_PUBLIC_WS_URL=wss://api.syntour.com

# Google Cloud 配置
GOOGLE_CLOUD_PROJECT=your-production-project-id
GOOGLE_CLOUD_LOCATION=us-central1
VERTEX_AI_ENDPOINT=your-production-endpoint
GOOGLE_APPLICATION_CREDENTIALS=/app/config/service-account-key.json
GCS_BUCKET_NAME=syntour-production-storage

# API Keys (使用环境变量或密钥管理服务)
GOOGLE_SPEECH_API_KEY=${GOOGLE_SPEECH_API_KEY}
GOOGLE_PLACES_API_KEY=${GOOGLE_PLACES_API_KEY}
NEXT_PUBLIC_GOOGLE_MAPS_JAVASCRIPT_API_KEY=${GOOGLE_MAPS_API_KEY}

# 第三方服务
AMADEUS_API_KEY=${AMADEUS_API_KEY}
AMADEUS_API_SECRET=${AMADEUS_API_SECRET}
TOMORROW_IO_API_KEY=${TOMORROW_IO_API_KEY}
FLIGHT_API_KEY=${FLIGHT_API_KEY}
GEOAPIFY_API_KEY=${GEOAPIFY_API_KEY}
HOTELBEDS_API_KEY=${HOTELBEDS_API_KEY}
HOTELBEDS_SECRET=${HOTELBEDS_SECRET}
OPEN_WEATHER_MAP_API_KEY=${OPENWEATHER_API_KEY}

# 数据库配置
DATABASE_URL=postgresql://syntour_user:${DB_PASSWORD}@localhost:5432/syntour_production
DB_POOL_MIN_SIZE=10
DB_POOL_MAX_SIZE=50

# Redis 配置
REDIS_URL=redis://localhost:6379/0

# 性能配置
MAX_CONCURRENT_TASKS=20
HEALTH_CHECK_INTERVAL=30.0
HTTP_POOL_MAX_SIZE=50
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60
CACHE_TTL_SECONDS=300

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=50MB
LOG_BACKUP_COUNT=10

# 安全配置
SECRET_KEY=${SECRET_KEY}
ALLOWED_HOSTS=syntour.com,api.syntour.com
CORS_ORIGINS=https://syntour.com,https://www.syntour.com
```

## 3. 依赖服务配置

### 3.1 PostgreSQL 数据库

**安装和配置**
```bash
# 安装 PostgreSQL
sudo apt install postgresql postgresql-contrib

# 创建数据库和用户
sudo -u postgres psql
CREATE DATABASE syntour_production;
CREATE USER syntour_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE syntour_production TO syntour_user;
ALTER USER syntour_user CREATEDB;
```

**生产配置 (/etc/postgresql/14/main/postgresql.conf)**
```ini
# 连接配置
max_connections = 200
shared_buffers = 2GB
effective_cache_size = 6GB
work_mem = 16MB
maintenance_work_mem = 512MB

# 日志配置
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000

# 性能优化
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
```

### 3.2 Redis 缓存

**配置文件 (/etc/redis/redis.conf)**
```ini
# 内存配置
maxmemory 2gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 安全配置
requirepass your_redis_password
bind 127.0.0.1
port 6379

# 日志配置
loglevel notice
logfile /var/log/redis/redis-server.log
```

### 3.3 Nginx 反向代理

**配置文件 (/etc/nginx/sites-available/syntour)**
```nginx
upstream backend {
    server 127.0.0.1:8000;
    keepalive 32;
}

upstream frontend {
    server 127.0.0.1:3000;
    keepalive 32;
}

server {
    listen 80;
    server_name syntour.com www.syntour.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name syntour.com www.syntour.com;

    # SSL 配置
    ssl_certificate /etc/letsencrypt/live/syntour.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/syntour.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 前端路由
    location / {
        proxy_pass http://frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # API 路由
    location /api/ {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # WebSocket 支持
    location /ws {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 文件上传大小限制
    client_max_body_size 50M;
}
```

## 4. 安全配置

### 4.1 API 密钥管理

**使用环境变量或密钥管理服务**
```bash
# 使用 AWS Secrets Manager
aws secretsmanager get-secret-value --secret-id syntour/api-keys

# 使用 HashiCorp Vault
vault kv get -field=api_key secret/syntour/google

# 使用 Kubernetes Secrets
kubectl create secret generic syntour-secrets \
  --from-literal=google-api-key='your-key' \
  --from-literal=amadeus-key='your-key'
```

### 4.2 网络安全

**防火墙配置**
```bash
# UFW 配置
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# 限制数据库访问
sudo ufw allow from 127.0.0.1 to any port 5432
sudo ufw allow from 127.0.0.1 to any port 6379
```

**SSL/TLS 证书**
```bash
# 使用 Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d syntour.com -d www.syntour.com

# 自动续期
sudo crontab -e
0 12 * * * /usr/bin/certbot renew --quiet
```

### 4.3 应用安全

**FastAPI 安全配置**
```python
# main.py 安全中间件
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware

# HTTPS 重定向
app.add_middleware(HTTPSRedirectMiddleware)

# 信任的主机
app.add_middleware(
    TrustedHostMiddleware, 
    allowed_hosts=["syntour.com", "*.syntour.com"]
)

# CORS 配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://syntour.com", "https://www.syntour.com"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)
```

## 5. 部署步骤

### 5.1 服务器准备

```bash
#!/bin/bash
# 服务器初始化脚本

# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础软件
sudo apt install -y git curl wget unzip software-properties-common

# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# 安装 Python 3.8+
sudo apt install -y python3.8 python3.8-venv python3-pip

# 安装数据库
sudo apt install -y postgresql postgresql-contrib redis-server

# 安装 Nginx
sudo apt install -y nginx

# 创建应用用户
sudo useradd -m -s /bin/bash syntour
sudo usermod -aG sudo syntour
```

### 5.2 应用部署

**部署脚本 (deploy.sh)**
```bash
#!/bin/bash
set -e

echo "🚀 开始部署 SynTour 生产环境..."

# 配置变量
APP_DIR="/opt/syntour"
BACKUP_DIR="/opt/syntour-backup"
USER="syntour"
BRANCH="main"

# 创建备份
echo "📦 创建备份..."
if [ -d "$APP_DIR" ]; then
    sudo cp -r $APP_DIR $BACKUP_DIR-$(date +%Y%m%d-%H%M%S)
fi

# 克隆或更新代码
echo "📥 更新代码..."
if [ ! -d "$APP_DIR" ]; then
    sudo git clone https://github.com/your-org/syntour.git $APP_DIR
else
    cd $APP_DIR
    sudo git fetch origin
    sudo git reset --hard origin/$BRANCH
fi

sudo chown -R $USER:$USER $APP_DIR
cd $APP_DIR

# 后端部署
echo "🐍 部署后端..."
cd backend

# 创建虚拟环境
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt

# 数据库迁移
echo "🗄️ 数据库迁移..."
# python manage.py migrate  # 如果有迁移脚本

# 前端部署
echo "📦 部署前端..."
cd ../frontend
npm ci --production
npm run build

# 重启服务
echo "🔄 重启服务..."
sudo systemctl restart syntour-backend
sudo systemctl restart syntour-frontend
sudo systemctl reload nginx

# 健康检查
echo "🏥 健康检查..."
sleep 10

if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 后端服务正常"
else
    echo "❌ 后端服务异常"
    exit 1
fi

if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常"
    exit 1
fi

echo "🎉 部署完成！"
echo "🌐 网站: https://syntour.com"
echo "📚 API 文档: https://api.syntour.com/docs"
```

### 5.3 系统服务配置

**后端服务 (/etc/systemd/system/syntour-backend.service)**
```ini
[Unit]
Description=SynTour Backend API
After=network.target postgresql.service redis.service
Requires=postgresql.service redis.service

[Service]
Type=exec
User=syntour
Group=syntour
WorkingDirectory=/opt/syntour/backend
Environment=PATH=/opt/syntour/backend/venv/bin
EnvironmentFile=/opt/syntour/.env.production
ExecStart=/opt/syntour/backend/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5

# 安全配置
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/opt/syntour

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
```

**前端服务 (/etc/systemd/system/syntour-frontend.service)**
```ini
[Unit]
Description=SynTour Frontend
After=network.target

[Service]
Type=exec
User=syntour
Group=syntour
WorkingDirectory=/opt/syntour/frontend
EnvironmentFile=/opt/syntour/.env.production
ExecStart=/usr/bin/npm start
Restart=always
RestartSec=5

# 安全配置
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/opt/syntour

[Install]
WantedBy=multi-user.target
```

**启用服务**
```bash
sudo systemctl daemon-reload
sudo systemctl enable syntour-backend syntour-frontend
sudo systemctl start syntour-backend syntour-frontend
```

## 6. 监控和日志

### 6.1 应用监控

**健康检查端点**
```python
# 已实现的健康检查
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "database": "healthy",
            "redis": "healthy",
            "google_cloud": "healthy"
        }
    }
```

**监控脚本 (monitor.sh)**
```bash
#!/bin/bash
# 系统监控脚本

LOG_FILE="/var/log/syntour/monitor.log"
ALERT_EMAIL="<EMAIL>"

# 检查服务状态
check_service() {
    local service=$1
    if ! systemctl is-active --quiet $service; then
        echo "$(date): $service is down" >> $LOG_FILE
        # 发送告警
        echo "Service $service is down" | mail -s "SynTour Alert" $ALERT_EMAIL
        return 1
    fi
    return 0
}

# 检查 HTTP 响应
check_http() {
    local url=$1
    local expected_code=$2
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" $url)
    
    if [ "$response_code" != "$expected_code" ]; then
        echo "$(date): $url returned $response_code" >> $LOG_FILE
        return 1
    fi
    return 0
}

# 检查磁盘空间
check_disk() {
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $usage -gt 80 ]; then
        echo "$(date): Disk usage is ${usage}%" >> $LOG_FILE
        echo "Disk usage is ${usage}%" | mail -s "SynTour Disk Alert" $ALERT_EMAIL
    fi
}

# 执行检查
check_service "syntour-backend"
check_service "syntour-frontend"
check_service "postgresql"
check_service "redis"
check_service "nginx"

check_http "http://localhost:8000/health" "200"
check_http "http://localhost:3000" "200"

check_disk

echo "$(date): Monitor check completed" >> $LOG_FILE
```

### 6.2 日志管理

**日志轮转配置 (/etc/logrotate.d/syntour)**
```
/opt/syntour/backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 syntour syntour
    postrotate
        systemctl reload syntour-backend
    endscript
}

/var/log/syntour/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 syntour syntour
}
```

**日志聚合配置**
```bash
# 使用 rsyslog 聚合日志
echo "*.* @@log-server:514" >> /etc/rsyslog.conf
sudo systemctl restart rsyslog

# 或使用 Fluentd/Filebeat 发送到 ELK Stack
```

### 6.3 性能监控

**Prometheus 配置 (prometheus.yml)**
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'syntour-backend'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
    
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
      
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['localhost:9187']
      
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['localhost:9121']
```

## 7. 性能优化

### 7.1 数据库优化

**索引优化**
```sql
-- 为常用查询创建索引
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY idx_trips_user_id ON trips(user_id);
CREATE INDEX CONCURRENTLY idx_trips_created_at ON trips(created_at DESC);

-- 复合索引
CREATE INDEX CONCURRENTLY idx_bookings_user_status ON bookings(user_id, status);

-- 分析表统计信息
ANALYZE;
```

**连接池优化**
```python
# 已实现的连接池配置
pool_config = PoolConfig(
    min_size=10,  # 生产环境增加最小连接数
    max_size=50,  # 生产环境增加最大连接数
    max_queries=50000,
    max_inactive_connection_lifetime=300.0,
    timeout=30.0
)
```

### 7.2 缓存策略

**Redis 缓存配置**
```python
# 缓存策略实现
CACHE_STRATEGIES = {
    "user_profile": {"ttl": 3600, "key_pattern": "user:{user_id}"},
    "search_results": {"ttl": 1800, "key_pattern": "search:{query_hash}"},
    "api_responses": {"ttl": 300, "key_pattern": "api:{endpoint}:{params_hash}"},
    "static_data": {"ttl": 86400, "key_pattern": "static:{data_type}"}
}
```

**CDN 配置**
```nginx
# Nginx 静态文件缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary Accept-Encoding;
    
    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_types text/css application/javascript image/svg+xml;
}
```

### 7.3 应用优化

**异步处理优化**
```python
# 已实现的异步任务管理
task_config = TaskConfig(
    max_concurrent_tasks=20,  # 生产环境增加并发数
    priority=TaskPriority.HIGH,
    timeout=60.0,
    retry_count=3
)
```

**API 响应优化**
```python
# 响应压缩和优化
from fastapi.middleware.gzip import GZipMiddleware

app.add_middleware(GZipMiddleware, minimum_size=1000)

# 响应缓存
@lru_cache(maxsize=1000)
def get_cached_response(key: str):
    # 缓存逻辑
    pass
```

## 8. 故障排除

### 8.1 常见问题诊断

**服务启动失败**
```bash
# 检查服务状态
sudo systemctl status syntour-backend
sudo systemctl status syntour-frontend

# 查看详细日志
sudo journalctl -u syntour-backend -f
sudo journalctl -u syntour-frontend -f

# 检查端口占用
sudo netstat -tlnp | grep :8000
sudo netstat -tlnp | grep :3000
```

**数据库连接问题**
```bash
# 检查 PostgreSQL 状态
sudo systemctl status postgresql

# 测试数据库连接
psql -h localhost -U syntour_user -d syntour_production -c "SELECT 1;"

# 检查连接数
psql -h localhost -U syntour_user -d syntour_production -c "SELECT count(*) FROM pg_stat_activity;"
```

**内存和性能问题**
```bash
# 检查内存使用
free -h
ps aux --sort=-%mem | head -10

# 检查磁盘空间
df -h
du -sh /opt/syntour/*

# 检查系统负载
top
htop
iostat -x 1
```

### 8.2 错误恢复

**自动恢复脚本 (recovery.sh)**
```bash
#!/bin/bash
# 自动恢复脚本

LOG_FILE="/var/log/syntour/recovery.log"

recover_service() {
    local service=$1
    echo "$(date): Attempting to recover $service" >> $LOG_FILE
    
    # 重启服务
    sudo systemctl restart $service
    sleep 10
    
    # 检查服务状态
    if systemctl is-active --quiet $service; then
        echo "$(date): $service recovered successfully" >> $LOG_FILE
        return 0
    else
        echo "$(date): Failed to recover $service" >> $LOG_FILE
        return 1
    fi
}

# 检查并恢复后端服务
if ! curl -f http://localhost:8000/health > /dev/null 2>&1; then
    recover_service "syntour-backend"
fi

# 检查并恢复前端服务
if ! curl -f http://localhost:3000 > /dev/null 2>&1; then
    recover_service "syntour-frontend"
fi

# 检查并恢复数据库
if ! pg_isready -h localhost -p 5432; then
    recover_service "postgresql"
fi

# 检查并恢复 Redis
if ! redis-cli ping > /dev/null 2>&1; then
    recover_service "redis"
fi
```

### 8.3 备份恢复

**数据库备份脚本**
```bash
#!/bin/bash
# 数据库备份脚本

BACKUP_DIR="/opt/backups/database"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="syntour_production"
DB_USER="syntour_user"

mkdir -p $BACKUP_DIR

# 创建备份
pg_dump -h localhost -U $DB_USER -d $DB_NAME | gzip > $BACKUP_DIR/syntour_$DATE.sql.gz

# 保留最近 30 天的备份
find $BACKUP_DIR -name "syntour_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: syntour_$DATE.sql.gz"
```

**应用备份脚本**
```bash
#!/bin/bash
# 应用备份脚本

BACKUP_DIR="/opt/backups/application"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/opt/syntour"

mkdir -p $BACKUP_DIR

# 备份应用代码和配置
tar -czf $BACKUP_DIR/syntour_app_$DATE.tar.gz \
    --exclude='node_modules' \
    --exclude='venv' \
    --exclude='*.log' \
    --exclude='.git' \
    $APP_DIR

# 备份配置文件
cp /opt/syntour/.env.production $BACKUP_DIR/env_$DATE
cp -r /etc/nginx/sites-available/syntour $BACKUP_DIR/nginx_$DATE

# 保留最近 7 天的备份
find $BACKUP_DIR -name "syntour_app_*.tar.gz" -mtime +7 -delete

echo "Application backup completed: syntour_app_$DATE.tar.gz"
```

## 9. 维护指南

### 9.1 定期维护任务

**每日维护**
```bash
#!/bin/bash
# 每日维护脚本 (crontab: 0 2 * * *)

# 清理临时文件
find /tmp -name "syntour_*" -mtime +1 -delete
find /opt/syntour/backend/logs -name "*.log.*" -mtime +7 -delete

# 数据库维护
psql -h localhost -U syntour_user -d syntour_production -c "VACUUM ANALYZE;"

# 清理 Redis 过期键
redis-cli FLUSHDB

# 检查磁盘空间
df -h | awk '$5 > 80 {print "Warning: " $1 " is " $5 " full"}'

# 更新系统包
sudo apt update && sudo apt list --upgradable
```

**每周维护**
```bash
#!/bin/bash
# 每周维护脚本 (crontab: 0 3 * * 0)

# 完整数据库备份
/opt/scripts/backup_database.sh

# 应用备份
/opt/scripts/backup_application.sh

# 日志轮转
logrotate -f /etc/logrotate.d/syntour

# 系统更新
sudo apt upgrade -y

# SSL 证书检查
certbot certificates

# 性能报告
echo "Weekly Performance Report - $(date)" > /var/log/syntour/weekly_report.log
ps aux --sort=-%cpu | head -10 >> /var/log/syntour/weekly_report.log
ps aux --sort=-%mem | head -10 >> /var/log/syntour/weekly_report.log
```

### 9.2 监控告警

**Crontab 配置**
```bash
# 编辑 crontab
sudo crontab -e

# 添加监控任务
*/5 * * * * /opt/scripts/monitor.sh
*/15 * * * * /opt/scripts/health_check.sh
0 2 * * * /opt/scripts/daily_maintenance.sh
0 3 * * 0 /opt/scripts/weekly_maintenance.sh
0 1 * * * /opt/scripts/backup_database.sh
```

**告警配置**
```bash
# 邮件告警配置
sudo apt install mailutils

# 配置 SMTP
echo "set smtp=smtp://smtp.gmail.com:587" >> ~/.mailrc
echo "set smtp-auth=login" >> ~/.mailrc
echo "set smtp-auth-user=<EMAIL>" >> ~/.mailrc
echo "set smtp-auth-password=your-password" >> ~/.mailrc
echo "set ssl-verify=ignore" >> ~/.mailrc
```

### 9.3 更新部署

**滚动更新脚本**
```bash
#!/bin/bash
# 滚动更新脚本

set -e

echo "🔄 开始滚动更新..."

# 健康检查
if ! curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "❌ 当前服务不健康，取消更新"
    exit 1
fi

# 创建备份
echo "📦 创建备份..."
/opt/scripts/backup_application.sh

# 更新代码
echo "📥 更新代码..."
cd /opt/syntour
git fetch origin
git checkout main
git pull origin main

# 更新依赖
echo "📦 更新依赖..."
cd backend
source venv/bin/activate
pip install -r requirements.txt

cd ../frontend
npm ci --production
npm run build

# 重启后端服务
echo "🔄 重启后端服务..."
sudo systemctl restart syntour-backend
sleep 10

# 健康检查
if ! curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "❌ 后端服务启动失败，回滚..."
    # 回滚逻辑
    exit 1
fi

# 重启前端服务
echo "🔄 重启前端服务..."
sudo systemctl restart syntour-frontend
sleep 10

# 最终健康检查
if curl -f http://localhost:3000 > /dev/null 2>&1 && curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 更新完成！"
else
    echo "❌ 更新失败，请检查服务状态"
    exit 1
fi
```

### 9.4 性能调优

**定期性能检查**
```bash
#!/bin/bash
# 性能检查脚本

echo "=== SynTour 性能报告 $(date) ==="

# CPU 使用率
echo "CPU 使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1

# 内存使用率
echo "内存使用率:"
free | grep Mem | awk '{printf "%.2f%%\n", $3/$2 * 100.0}'

# 磁盘使用率
echo "磁盘使用率:"
df -h | grep -E '^/dev/' | awk '{print $1 ": " $5}'

# 数据库连接数
echo "数据库连接数:"
psql -h localhost -U syntour_user -d syntour_production -t -c "SELECT count(*) FROM pg_stat_activity;"

# Redis 内存使用
echo "Redis 内存使用:"
redis-cli info memory | grep used_memory_human

# 应用响应时间
echo "API 响应时间:"
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8000/health

# 网络连接数
echo "网络连接数:"
netstat -an | grep :8000 | wc -l
netstat -an | grep :3000 | wc -l
```

## 10. 安全检查清单

### 10.1 部署前检查

- [ ] 所有 API 密钥已从代码中移除，使用环境变量
- [ ] 数据库密码已更改为强密码
- [ ] SSL/TLS 证书已配置
- [ ] 防火墙规则已设置
- [ ] 不必要的端口已关闭
- [ ] 系统用户权限已最小化
- [ ] 日志记录已启用
- [ ] 备份策略已实施
- [ ] 监控告警已配置
- [ ] 错误页面不暴露敏感信息

### 10.2 运行时检查

- [ ] 定期更新系统补丁
- [ ] 监控异常访问模式
- [ ] 检查日志中的安全事件
- [ ] 验证备份完整性
- [ ] 测试灾难恢复流程
- [ ] 审查用户权限
- [ ] 检查第三方服务状态
- [ ] 验证 SSL 证书有效期
- [ ] 监控资源使用情况
- [ ] 检查依赖包安全漏洞

---

## 总结

本文档提供了 SynTour 项目的完整生产级部署指南，涵盖了从基础设施配置到日常维护的所有方面。在实际部署时，请根据具体的生产环境需求调整配置参数，并确保所有安全措施得到正确实施。

定期回顾和更新本文档，确保部署流程与项目发展保持同步。如有问题，请参考故障排除章节或联系技术团队。