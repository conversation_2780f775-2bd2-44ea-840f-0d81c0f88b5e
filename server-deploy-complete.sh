#!/bin/bash

# SynTour 服务器完整部署脚本
# 在服务器上运行此脚本完成所有部署操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "========================================"
echo "SynTour 完整部署脚本"
echo "========================================"

# 检查当前目录
if [ ! -f "docker-compose.production.yml" ]; then
    log_error "请在项目根目录运行此脚本"
    exit 1
fi

# 1. 环境变量配置
log_info "步骤 1: 配置环境变量"
if [ ! -f ".env.production" ]; then
    if [ -f ".env.production.example" ]; then
        cp .env.production.example .env.production
        log_warning "已创建 .env.production 文件，请编辑并设置正确的密码"
        echo "请设置以下环境变量："
        echo "- POSTGRES_PASSWORD=your_secure_password"
        echo "- REDIS_PASSWORD=your_secure_password"
        echo "- JWT_SECRET=your_very_long_secret_key"
        echo
        read -p "是否现在编辑环境变量文件？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            nano .env.production
        else
            log_error "请手动编辑 .env.production 文件后重新运行脚本"
            exit 1
        fi
    else
        log_error "未找到环境变量模板文件"
        exit 1
    fi
else
    log_success "环境变量文件已存在"
fi

# 2. 创建必要目录
log_info "步骤 2: 创建必要目录"
mkdir -p uploads logs nginx/ssl
chmod 755 uploads logs
chown -R 1001:1001 uploads logs 2>/dev/null || true
log_success "目录创建完成"

# 3. 防火墙配置
log_info "步骤 3: 配置防火墙"
ufw allow 22/tcp   # SSH
ufw allow 80/tcp   # HTTP
ufw allow 443/tcp  # HTTPS
ufw --force enable
log_success "防火墙配置完成"

# 4. 停止现有服务
log_info "步骤 4: 停止现有服务"
docker-compose -f docker-compose.production.yml down --remove-orphans || true
log_success "现有服务已停止"

# 5. 清理旧资源（可选）
read -p "是否清理旧的Docker资源？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "清理Docker资源..."
    docker system prune -f
    docker image prune -f
    log_success "Docker资源清理完成"
fi

# 6. 构建并启动服务
log_info "步骤 6: 构建并启动服务"
docker-compose -f docker-compose.production.yml up --build -d
log_success "服务构建和启动完成"

# 7. 等待服务启动
log_info "步骤 7: 等待服务启动"
sleep 45

# 8. 健康检查
log_info "步骤 8: 执行健康检查"

# 检查容器状态
log_info "检查容器状态..."
docker-compose -f docker-compose.production.yml ps

# 检查后端健康
log_info "检查后端健康状态..."
for i in {1..12}; do
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "后端服务健康检查通过"
        break
    else
        log_warning "等待后端服务启动... ($i/12)"
        sleep 10
    fi
    
    if [ $i -eq 12 ]; then
        log_error "后端服务健康检查失败"
        log_info "查看后端日志:"
        docker-compose -f docker-compose.production.yml logs backend --tail=30
        exit 1
    fi
done

# 检查前端
log_info "检查前端状态..."
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    log_success "前端服务运行正常"
else
    log_warning "前端服务可能未完全启动，请稍后检查"
fi

# 9. 显示部署结果
echo
echo "========================================"
log_success "🎉 SynTour 部署完成！"
echo "========================================"
echo
echo "服务访问地址:"
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || echo "************")
echo "- 前端应用: http://$SERVER_IP"
echo "- API接口: http://$SERVER_IP/api"
echo "- 健康检查: http://$SERVER_IP/health"
echo
echo "管理命令:"
echo "- 查看日志: docker-compose -f docker-compose.production.yml logs -f"
echo "- 重启服务: docker-compose -f docker-compose.production.yml restart"
echo "- 停止服务: docker-compose -f docker-compose.production.yml down"
echo
echo "下一步建议:"
echo "1. 配置域名解析指向服务器IP"
echo "2. 申请和配置SSL证书"
echo "3. 设置定期数据库备份"
echo "4. 配置监控和告警系统"
