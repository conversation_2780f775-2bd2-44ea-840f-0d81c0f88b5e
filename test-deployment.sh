#!/bin/bash

# SynTour 部署测试脚本
# 用于测试修复后的部署配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试Docker构建
log_info "测试后端Docker构建..."
if docker build -f Dockerfile.backend -t syntour-backend-test .; then
    log_success "后端Docker构建成功"
    docker rmi syntour-backend-test
else
    log_error "后端Docker构建失败"
    exit 1
fi

# 测试前端Docker构建
log_info "测试前端Docker构建..."
if docker build -f Dockerfile.frontend -t syntour-frontend-test .; then
    log_success "前端Docker构建成功"
    docker rmi syntour-frontend-test
else
    log_error "前端Docker构建失败"
    exit 1
fi

# 验证docker-compose配置
log_info "验证docker-compose配置..."
if docker-compose -f docker-compose.production.yml config > /dev/null; then
    log_success "docker-compose配置验证通过"
else
    log_error "docker-compose配置验证失败"
    exit 1
fi

# 检查必要文件
log_info "检查必要文件..."
required_files=(
    "Dockerfile.backend"
    "Dockerfile.frontend"
    "docker-compose.production.yml"
    "nginx/production.conf"
    "backend/requirements-core.txt"
    "backend/main.py"
    "backend/__init__.py"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        log_success "✓ $file"
    else
        log_error "✗ $file 缺失"
        exit 1
    fi
done

log_success "所有测试通过！可以开始部署。"
echo
echo "下一步："
echo "1. 确保.env.production文件已正确配置"
echo "2. 运行: chmod +x deploy-fix.sh && ./deploy-fix.sh"
