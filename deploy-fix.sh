#!/bin/bash

# SynTour 部署修复脚本
# 用于修复当前部署问题并重新启动服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
if [[ $EUID -eq 0 ]]; then
   log_error "请不要以root用户运行此脚本"
   exit 1
fi

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    log_error "Docker未运行，请先启动Docker"
    exit 1
fi

# 检查环境变量文件
if [ ! -f ".env.production" ]; then
    log_warning "未找到.env.production文件"
    if [ -f ".env.production.example" ]; then
        log_info "复制示例环境变量文件..."
        cp .env.production.example .env.production
        log_warning "请编辑.env.production文件并设置正确的环境变量"
        exit 1
    else
        log_error "未找到环境变量文件，请创建.env.production"
        exit 1
    fi
fi

# 创建必要的目录
log_info "创建必要的目录..."
mkdir -p uploads logs nginx/ssl

# 设置权限
log_info "设置目录权限..."
chmod 755 uploads logs
sudo chown -R 1001:1001 uploads logs 2>/dev/null || true

# 停止现有容器
log_info "停止现有容器..."
docker-compose -f docker-compose.production.yml down --remove-orphans || true

# 清理旧镜像（可选）
read -p "是否要清理旧的Docker镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "清理旧镜像..."
    docker system prune -f
    docker image prune -f
fi

# 构建并启动服务
log_info "构建并启动服务..."
docker-compose -f docker-compose.production.yml up --build -d

# 等待服务启动
log_info "等待服务启动..."
sleep 30

# 检查服务状态
log_info "检查服务状态..."
docker-compose -f docker-compose.production.yml ps

# 检查后端健康状态
log_info "检查后端健康状态..."
for i in {1..10}; do
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "后端服务健康检查通过"
        break
    else
        log_warning "等待后端服务启动... ($i/10)"
        sleep 10
    fi
    
    if [ $i -eq 10 ]; then
        log_error "后端服务健康检查失败"
        log_info "查看后端日志:"
        docker-compose -f docker-compose.production.yml logs backend --tail=20
        exit 1
    fi
done

# 检查前端状态
log_info "检查前端状态..."
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    log_success "前端服务运行正常"
else
    log_warning "前端服务可能未完全启动"
fi

# 显示服务信息
log_success "部署完成！"
echo
echo "服务访问地址:"
echo "- 前端: http://$(curl -s ifconfig.me 2>/dev/null || echo 'localhost')"
echo "- API: http://$(curl -s ifconfig.me 2>/dev/null || echo 'localhost')/api"
echo "- 健康检查: http://$(curl -s ifconfig.me 2>/dev/null || echo 'localhost')/health"
echo
echo "管理命令:"
echo "- 查看日志: docker-compose -f docker-compose.production.yml logs -f"
echo "- 重启服务: docker-compose -f docker-compose.production.yml restart"
echo "- 停止服务: docker-compose -f docker-compose.production.yml down"
