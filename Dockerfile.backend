# 后端Dockerfile - FastAPI生产环境
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libmagic1 \
    libmagic-dev \
    curl \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY backend/requirements-core.txt ./requirements.txt

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# 安装生产环境额外依赖
RUN pip install --no-cache-dir \
    psycopg2-binary==2.9.9 \
    redis==5.0.1 \
    gunicorn==21.2.0 \
    uvicorn[standard]==0.24.0 \
    python-multipart==0.0.6 \
    bcrypt==4.1.2 \
    python-jose[cryptography]==3.3.0

# 创建必要目录
RUN mkdir -p /app/uploads /app/config /app/logs

# 复制后端代码
COPY backend/ ./backend/

# 创建主应用入口点
COPY backend/main.py ./main.py

# 创建用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app

USER app

# 设置Python路径 - 包含backend目录
ENV PYTHONPATH=/app:/app/backend
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 使用Uvicorn作为ASGI服务器
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]