# 后端Dockerfile - FastAPI生产环境
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libmagic1 \
    libmagic-dev \
    curl \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY backend/requirements-core.txt ./requirements.txt

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 安装生产环境额外依赖
RUN pip install --no-cache-dir \
    psycopg2-binary \
    redis \
    gunicorn \
    uvicorn[standard] \
    python-multipart

# 复制后端代码到根目录
COPY backend/ ./

# 创建必要目录
RUN mkdir -p /app/uploads /app/config /app/logs

# 创建用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app

USER app

# 设置Python路径
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8000

# 使用Uvicorn作为ASGI服务器
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "1"]