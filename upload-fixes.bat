@echo off
echo ========================================
echo SynTour 修复文件上传脚本
echo ========================================

set SERVER=8.210.115.80
set USER=root
set REMOTE_PATH=/opt/syntour

echo 正在上传修复后的文件到服务器...

echo.
echo 1. 上传 Dockerfile.backend
scp Dockerfile.backend %USER%@%SERVER%:%REMOTE_PATH%/

echo.
echo 2. 上传 docker-compose.production.yml
scp docker-compose.production.yml %USER%@%SERVER%:%REMOTE_PATH%/

echo.
echo 3. 上传 backend/requirements-core.txt
scp backend/requirements-core.txt %USER%@%SERVER%:%REMOTE_PATH%/backend/

echo.
echo 4. 上传部署脚本
scp deploy-fix.sh %USER%@%SERVER%:%REMOTE_PATH%/
scp test-deployment.sh %USER%@%SERVER%:%REMOTE_PATH%/

echo.
echo 5. 上传环境变量模板
scp .env.production.example %USER%@%SERVER%:%REMOTE_PATH%/

echo.
echo 6. 上传部署指南
scp SERVER_DEPLOYMENT_INSTRUCTIONS.md %USER%@%SERVER%:%REMOTE_PATH%/

echo.
echo ========================================
echo 文件上传完成！
echo ========================================
echo.
echo 下一步：
echo 1. SSH连接到服务器: ssh %USER%@%SERVER%
echo 2. 进入项目目录: cd %REMOTE_PATH%
echo 3. 配置环境变量: cp .env.production.example .env.production
echo 4. 编辑环境变量: nano .env.production
echo 5. 运行部署: ./deploy-fix.sh
echo.
pause
