import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
import ssl
import os
import random
import string
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import logging
from dataclasses import dataclass

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class EmailConfig:
    """邮箱配置类"""
    host: str
    port: int
    secure: bool
    user: str
    password: str
    from_email: str

class EmailService:
    """邮箱服务类"""
    
    def __init__(self):
        self.config = self._load_config()
        self.verification_codes: Dict[str, Dict[str, Any]] = {}
    
    def _load_config(self) -> EmailConfig:
        """加载邮箱配置"""
        return EmailConfig(
            host=os.getenv('EMAIL_HOST', 'smtpdm.aliyun.com'),
            port=int(os.getenv('EMAIL_PORT', '465')),
            secure=os.getenv('EMAIL_SECURE', 'true').lower() == 'true',
            user=os.getenv('EMAIL_USER', ''),
            password=os.getenv('EMAIL_PASSWORD', ''),
            from_email=os.getenv('EMAIL_FROM', 'SynTour <<EMAIL>>')
        )
    
    def _create_smtp_connection(self):
        """创建SMTP连接"""
        try:
            if self.config.secure:
                context = ssl.create_default_context()
                server = smtplib.SMTP_SSL(self.config.host, self.config.port, context=context)
            else:
                server = smtplib.SMTP(self.config.host, self.config.port)
                server.starttls()
            
            server.login(self.config.user, self.config.password)
            return server
        except Exception as e:
            logger.error(f"创建SMTP连接失败: {e}")
            raise
    
    def generate_verification_code(self, length: int = 6) -> str:
        """生成验证码"""
        return ''.join(random.choices(string.digits, k=length))
    
    def send_verification_email(self, to_email: str, code: str, purpose: str = "登录") -> bool:
        """发送验证码邮件"""
        try:
            # 创建邮件内容
            subject = f"SynTour {purpose}验证码"
            html_content = self._create_verification_email_template(code, purpose)
            
            # 发送邮件
            success = self._send_email(to_email, subject, html_content)
            
            if success:
                # 存储验证码（5分钟有效期）
                self.verification_codes[to_email] = {
                    'code': code,
                    'purpose': purpose,
                    'created_at': datetime.now(),
                    'expires_at': datetime.now() + timedelta(minutes=5)
                }
                logger.info(f"验证码邮件已发送到: {to_email}")
            
            return success
        except Exception as e:
            logger.error(f"发送验证码邮件失败: {e}")
            return False
    
    def verify_code(self, email: str, code: str, purpose: str = "登录") -> bool:
        """验证验证码"""
        try:
            if email not in self.verification_codes:
                logger.warning(f"邮箱 {email} 没有验证码记录")
                return False
            
            stored_data = self.verification_codes[email]
            
            # 检查验证码是否过期
            if datetime.now() > stored_data['expires_at']:
                logger.warning(f"邮箱 {email} 的验证码已过期")
                del self.verification_codes[email]
                return False
            
            # 检查验证码和用途是否匹配
            if stored_data['code'] == code and stored_data['purpose'] == purpose:
                # 验证成功，删除验证码
                del self.verification_codes[email]
                logger.info(f"邮箱 {email} 验证码验证成功")
                return True
            else:
                logger.warning(f"邮箱 {email} 验证码不匹配")
                return False
        except Exception as e:
            logger.error(f"验证码验证失败: {e}")
            return False
    
    def send_welcome_email(self, to_email: str, username: str) -> bool:
        """发送欢迎邮件"""
        try:
            subject = "欢迎加入SynTour！"
            html_content = self._create_welcome_email_template(username)
            
            success = self._send_email(to_email, subject, html_content)
            
            if success:
                logger.info(f"欢迎邮件已发送到: {to_email}")
            
            return success
        except Exception as e:
            logger.error(f"发送欢迎邮件失败: {e}")
            return False
    
    def send_password_reset_email(self, to_email: str, reset_token: str) -> bool:
        """发送密码重置邮件"""
        try:
            subject = "SynTour 密码重置"
            html_content = self._create_password_reset_email_template(reset_token)
            
            success = self._send_email(to_email, subject, html_content)
            
            if success:
                logger.info(f"密码重置邮件已发送到: {to_email}")
            
            return success
        except Exception as e:
            logger.error(f"发送密码重置邮件失败: {e}")
            return False
    
    def _send_email(self, to_email: str, subject: str, html_content: str) -> bool:
        """发送邮件的通用方法"""
        try:
            # 创建邮件对象
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = self.config.from_email
            message["To"] = to_email
            
            # 添加HTML内容
            html_part = MIMEText(html_content, "html", "utf-8")
            message.attach(html_part)
            
            # 发送邮件
            with self._create_smtp_connection() as server:
                server.send_message(message)
            
            return True
        except Exception as e:
            logger.error(f"发送邮件失败: {e}")
            return False
    
    def _create_verification_email_template(self, code: str, purpose: str) -> str:
        """创建验证码邮件模板"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>SynTour 验证码</title>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                .content {{ padding: 40px 30px; }}
                .code-box {{ background-color: #f8f9fa; border: 2px dashed #667eea; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0; }}
                .code {{ font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 5px; }}
                .footer {{ background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; color: #666; font-size: 14px; }}
                .warning {{ color: #e74c3c; font-size: 14px; margin-top: 15px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🌟 SynTour</h1>
                    <p>您的智能旅行助手</p>
                </div>
                <div class="content">
                    <h2>您的{purpose}验证码</h2>
                    <p>您好！感谢您使用SynTour。请使用以下验证码完成{purpose}：</p>
                    
                    <div class="code-box">
                        <div class="code">{code}</div>
                    </div>
                    
                    <p>此验证码将在 <strong>5分钟</strong> 后失效，请尽快使用。</p>
                    
                    <div class="warning">
                        ⚠️ 如果这不是您本人的操作，请忽略此邮件。
                    </div>
                </div>
                <div class="footer">
                    <p>© 2024 SynTour. 让每一次旅行都充满智慧。</p>
                    <p>此邮件由系统自动发送，请勿回复。</p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def _create_welcome_email_template(self, username: str) -> str:
        """创建欢迎邮件模板"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>欢迎加入SynTour</title>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                .content {{ padding: 40px 30px; }}
                .feature {{ background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #667eea; }}
                .footer {{ background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; color: #666; font-size: 14px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎉 欢迎加入SynTour！</h1>
                    <p>开启您的智能旅行之旅</p>
                </div>
                <div class="content">
                    <h2>亲爱的 {username}，</h2>
                    <p>欢迎加入SynTour大家庭！我们很高兴您选择我们作为您的旅行伙伴。</p>
                    
                    <h3>🌟 您可以享受的功能：</h3>
                    <div class="feature">
                        <strong>🤖 AI旅行规划</strong><br>
                        智能分析您的偏好，为您定制专属旅行计划
                    </div>
                    <div class="feature">
                        <strong>📸 图像识别</strong><br>
                        上传照片即可获得景点信息和旅行建议
                    </div>
                    <div class="feature">
                        <strong>💬 智能对话</strong><br>
                        随时与AI助手交流，获得旅行灵感和建议
                    </div>
                    
                    <p>现在就开始探索吧！如有任何问题，随时联系我们的客服团队。</p>
                </div>
                <div class="footer">
                    <p>© 2024 SynTour. 让每一次旅行都充满智慧。</p>
                    <p>如需帮助，请访问我们的帮助中心或联系客服。</p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def _create_password_reset_email_template(self, reset_token: str) -> str:
        """创建密码重置邮件模板"""
        reset_url = f"https://syntour.com/auth/reset-password?token={reset_token}"
        
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>SynTour 密码重置</title>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                .content {{ padding: 40px 30px; }}
                .button {{ display: inline-block; background-color: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
                .footer {{ background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; color: #666; font-size: 14px; }}
                .warning {{ color: #e74c3c; font-size: 14px; margin-top: 15px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔐 SynTour</h1>
                    <p>密码重置请求</p>
                </div>
                <div class="content">
                    <h2>重置您的密码</h2>
                    <p>我们收到了您的密码重置请求。请点击下面的按钮来重置您的密码：</p>
                    
                    <div style="text-align: center;">
                        <a href="{reset_url}" class="button">重置密码</a>
                    </div>
                    
                    <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                    <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">{reset_url}</p>
                    
                    <p>此链接将在 <strong>1小时</strong> 后失效。</p>
                    
                    <div class="warning">
                        ⚠️ 如果这不是您本人的操作，请忽略此邮件，您的密码不会被更改。
                    </div>
                </div>
                <div class="footer">
                    <p>© 2024 SynTour. 让每一次旅行都充满智慧。</p>
                    <p>此邮件由系统自动发送，请勿回复。</p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def cleanup_expired_codes(self):
        """清理过期的验证码"""
        current_time = datetime.now()
        expired_emails = [
            email for email, data in self.verification_codes.items()
            if current_time > data['expires_at']
        ]
        
        for email in expired_emails:
            del self.verification_codes[email]
        
        if expired_emails:
            logger.info(f"清理了 {len(expired_emails)} 个过期验证码")

# 创建全局邮箱服务实例
email_service = EmailService()