# SynTour 生产环境部署

## 项目概述

SynTour 是一个智能旅游规划平台，采用前后端分离架构：
- **后端**: FastAPI + Python + PostgreSQL + Redis
- **前端**: Next.js + React + TypeScript
- **云服务**: Google Cloud (图片识别、语音处理、文件存储)

## 部署文件说明

### 配置文件
- `.env.production` - 生产环境配置文件
- `database-init.sql` - 数据库初始化脚本
- `google-cloud-setup.md` - Google Cloud 配置详细指南

### 部署脚本
- `server-setup.sh` - 服务器环境初始化脚本
- `deploy.sh` - 项目部署脚本
- `quick-deploy.sh` - 一键部署脚本（推荐）
- `monitor.sh` - 系统监控和健康检查脚本

### 文档
- `DEPLOYMENT.md` - 详细部署指南
- `README.md` - 本文件

## 快速开始

### 一键部署（推荐）

#### Linux/macOS 用户

```bash
# 1. 给脚本执行权限
chmod +x *.sh

# 2. 执行一键部署
./quick-deploy.sh
```

#### Windows 用户

**方法一：使用 WSL (推荐)**

```bash
# 在 WSL 中执行
wsl
chmod +x *.sh
./quick-deploy.sh
```

**方法二：使用 Git Bash**

```bash
# 在 Git Bash 中执行
chmod +x *.sh
./quick-deploy.sh
```

**方法三：手动部署**

如果无法使用 shell 脚本，请按照以下步骤手动部署：

1. **上传服务器初始化脚本**
   ```cmd
   scp server-setup.sh root@iZj6c4zk6mwb25zt3q3e34Z:/tmp/
   ```

2. **SSH 连接服务器并初始化**
   ```cmd
   ssh root@iZj6c4zk6mwb25zt3q3e34Z
   chmod +x /tmp/server-setup.sh
   /tmp/server-setup.sh
   ```

3. **初始化数据库**
   ```cmd
   scp database-init.sql root@iZj6c4zk6mwb25zt3q3e34Z:/tmp/
   ssh root@iZj6c4zk6mwb25zt3q3e34Z "sudo -u postgres psql -f /tmp/database-init.sql"
   ```

4. **部署项目**
   ```cmd
   scp deploy.sh root@iZj6c4zk6mwb25zt3q3e34Z:/tmp/
   ssh root@iZj6c4zk6mwb25zt3q3e34Z "chmod +x /tmp/deploy.sh && /tmp/deploy.sh"
   ```

## 部署步骤详解

### 1. 服务器环境准备

`server-setup.sh` 脚本将自动完成：
- 系统更新和基础工具安装
- Node.js 20.x 安装
- Python 3.11 安装和配置
- PostgreSQL 15 安装和配置
- Redis 安装和配置
- Nginx 安装和配置
- 防火墙配置
- 系统服务创建

### 2. 数据库初始化

`database-init.sql` 脚本将创建：
- 数据库表结构
- 索引和约束
- 触发器和函数
- 初始数据（管理员和测试用户）

### 3. Google Cloud 配置

**重要**: 部署前必须配置 Google Cloud 服务账户密钥

请参考 `google-cloud-setup.md` 文件中的详细说明：
1. 创建或获取服务账户密钥文件
2. 上传到服务器指定位置
3. 设置正确的文件权限

### 4. 项目部署

`deploy.sh` 或 `quick-deploy.sh` 将完成：
- 项目文件上传
- 依赖安装
- 应用构建
- 服务启动
- 健康检查

## 部署后验证

### 访问应用

- **前端应用**: http://iZj6c4zk6mwb25zt3q3e34Z
- **API 文档**: http://iZj6c4zk6mwb25zt3q3e34Z:8000/docs
- **健康检查**: http://iZj6c4zk6mwb25zt3q3e34Z:8000/health

### 默认账户

- **管理员**: <EMAIL> / admin123
- **测试用户**: <EMAIL> / test123

⚠️ **请在生产环境中立即更改默认密码！**

### 系统监控

使用监控脚本检查系统状态：

```bash
# Linux/macOS
./monitor.sh --all

# Windows (WSL/Git Bash)
bash monitor.sh --all
```

## 服务管理

### 服务状态检查

```bash
ssh root@iZj6c4zk6mwb25zt3q3e34Z
sudo systemctl status syntour-backend syntour-frontend nginx postgresql redis-server
```

### 服务重启

```bash
ssh root@iZj6c4zk6mwb25zt3q3e34Z
sudo systemctl restart syntour-backend syntour-frontend
```

### 查看日志

```bash
ssh root@iZj6c4zk6mwb25zt3q3e34Z
# 后端日志
sudo journalctl -u syntour-backend -f
# 前端日志
sudo journalctl -u syntour-frontend -f
```

## 故障排除

### 常见问题

1. **脚本权限问题**
   - Linux/macOS: `chmod +x *.sh`
   - Windows: 使用 WSL 或 Git Bash

2. **SSH 连接问题**
   - 检查服务器 IP 和端口
   - 确认用户名和密码
   - 检查网络连接

3. **服务启动失败**
   - 查看服务日志: `journalctl -u service-name`
   - 检查配置文件
   - 验证依赖服务状态

4. **Google Cloud 认证失败**
   - 检查服务账户密钥文件
   - 验证文件路径和权限
   - 参考 `google-cloud-setup.md`

### 获取帮助

如果遇到问题，请：
1. 查看 `DEPLOYMENT.md` 详细文档
2. 运行监控脚本诊断问题
3. 检查相关日志文件
4. 验证网络和防火墙设置

## 安全注意事项

1. **立即更改默认密码**
   - 数据库密码
   - Redis 密码
   - 应用管理员密码

2. **配置 HTTPS**
   - 获取 SSL 证书
   - 更新 Nginx 配置

3. **定期更新**
   - 系统安全更新
   - 应用依赖更新
   - 密钥轮换

## 性能优化

1. **数据库优化**
   - 定期维护和清理
   - 监控查询性能
   - 调整连接池

2. **缓存优化**
   - Redis 内存调优
   - 缓存策略优化

3. **应用优化**
   - Worker 进程调优
   - 静态文件缓存
   - CDN 配置

## 备份和恢复

### 数据库备份

```bash
# 创建备份
pg_dump -U syntour_user syntour_production > backup_$(date +%Y%m%d).sql

# 恢复备份
sudo -u postgres psql -d syntour_production < backup_file.sql
```

### 文件备份

```bash
# 备份应用文件
tar -czf syntour_backup_$(date +%Y%m%d).tar.gz /opt/syntour
```

---

**部署完成后，请访问应用并验证所有功能正常工作！**

如需更详细的信息，请查看 `DEPLOYMENT.md` 文件。
