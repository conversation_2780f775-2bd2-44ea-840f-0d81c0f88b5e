# SynTour Production Environment Variables
# Copy this file to .env.production and fill in the actual values

# Database Configuration
POSTGRES_PASSWORD=your_secure_postgres_password_here

# Redis Configuration  
REDIS_PASSWORD=your_secure_redis_password_here

# JWT Configuration
JWT_SECRET=your_very_secure_jwt_secret_key_here_at_least_32_characters

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# Google Cloud Configuration (Optional)
GOOGLE_CLOUD_PROJECT=your_project_id
GOOGLE_CLOUD_LOCATION=us-central1

# Application Configuration
ENVIRONMENT=production
DEBUG=false
